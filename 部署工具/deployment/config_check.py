#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : config_check.py.py
# @Time    : 2025/06/03 16:31
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys
import yaml
from dataclasses import dataclass
from rich.console import Console

console = Console()

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))

"""
检查配置文件是否符合规范
"""


@dataclass
class DeploymentSchema:
    """
    Deployment schema class. | 部署模式类
    """
    root_user: bool = False
    roles_memory_size: dict = None
    maintenance_token: str = None  # 维护模式token
    keep_package: bool = True  # 是否保留安装包
    # single_machine: bool = True  # 是否单机部署
    deployment_mode: str = "standalone"
    skip_pre_check: bool = False  # 是否跳过预检查
    deploy_whaletunnel: bool = False  # 是否部署whaletunnel
    deployment_dir: str = "/data/whalestudio"  # 部署目录
    package_dir: str = "/data/whalestudio/packages"
    service_log_dir: str = "/data/whalestudio/logs"  # 服务日志目录
    data_basedir_path: str = "/data/whalestudio/data_basedir"  # 用户数据缓存目录
    datasource_encrypted: bool = True  # 数据源加密
    tenant_enable: bool = True  # 是否启用租户模式
    api_workflow_address: str = "http://localhost:12345"  # API 地址
    local_ip: str = "127.0.0.1"  # 本地IP地址
    download_port: int = 18889  # 下载端口
    reset_password_email: dict = None  # 重置密码邮件配置
    registration_center: dict = None  # 注册中心配置
    metabase: dict = None
    system_environment_path: str = "/etc/profile"  # 系统环境变量配置文件路径
    process_pool_size: int = 10  # 进程池大小
    unified_deployment_user: dict = None  # 统一部署用户配置
    cluster_nodes: dict = None  # 集群节点配置
    whalestudio_environment: dict = None
    resource_center: dict = None  # 资源中心配置


def error_handler(message):
    console.print(f"[bold red]Error: {message}[/bold red]")


def info_handler(message):
    console.print(f"[bold green]{message}[/bold green]")


config_file = os.path.join(".", "config", 'deployment.yaml')
if not os.path.isfile(config_file):
    error_handler(
        message = "The configuration file does not exist. Please check if there is a deployment.uml file in the config directory"
    )
    error_handler(message = f"Configuration file path: {config_file}")
    sys.exit(1)

if not config_file.endswith('.yaml'):
    error_handler(
        message = "The configuration file is not a YAML file. Please check if the deployment.yaml file exists in the config directory"
    )
    error_handler(message = f"Configuration file path:{config_file}")
    sys.exit(1)

try:
    with open(config_file, 'r') as f:
        deployment_config = yaml.safe_load(f)
except yaml.YAMLError as e:
    if hasattr(e, 'problem_mark'):
        line = e.problem_mark.line + 1
        error_handler(message = f"Configuration file syntax error configuration file path: {config_file}")
        error_handler(message = f"Error line number:{line}")
        sys.exit(1)
    else:
        error_handler(message = f"Configuration file syntax error: {e}")
        error_handler(message = f"Configuration file path:{config_file}")
        sys.exit(1)
except Exception as e:
    error_handler(message = f"Configuration file syntax error:{e}")
    error_handler(message = f"Configuration file path:{config_file}")
    sys.exit(1)

# 检查文件内容是否符合schema格式
try:
    deployment_schema = DeploymentSchema(
        **deployment_config
    )
except Exception as e:
    error_handler(message = f"Format error in configuration file content:{e}")
    error_handler(message = f"Configuration file path:{config_file}")
    sys.exit(1)

info_handler(message = f"Configuration file check passed, configuration file path: {config_file}")
