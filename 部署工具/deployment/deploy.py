#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : deploy.py
# @Time    : 2025/06/03 16:33
# <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import sys
import argparse

from app.common.logging_utils import setup_logging, error_handler
from app.common.utils import startup_user
from app.config.deployment_config import deployment_config

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


def deploy_whale_studio(deploy_args, host = None):
    """Deploy the WhaleStudio. | 部署 WhaleStudio"""
    deploy_operation = {k: v for k, v in deploy_args.items() if v}
    if not deploy_operation:
        error_handler(message = "No operation specified. Please specify at least one operation.")
        sys.exit(1)
    # 操作类型, 值
    operation_type, operation_value = deploy_operation.popitem()
    match deployment_config.deployment_mode.lower():
        case "standalone":
            from app.standaone.main import standalone_main
            standalone_main(operation_type = operation_type, operation_value = operation_value)
        case "cluster" | "clusters":
            from app.cluster.main import ClusterMain
            ClusterMain(
                operation_type = operation_type, operation_value = operation_value,
                host = host
            ).run()
        case _:
            error_handler(
                message = "Invalid deployment mode. Please check the deployment mode in the configuration file."
            )
            sys.exit(1)


def main():
    """Main function of the script. | 脚本主函数"""

    parser = argparse.ArgumentParser(description = 'This is a WhaleStudio Deployment Tool.')
    parser.add_argument(
        "--log_level", type = str, default = "INFO",
        help = "Set the log level. | 设置日志级别 (DEBUG/INFO , default: INFO)",
        choices = ["DEBUG", "INFO"]
    )

    parser.add_argument(
        "--host", type = str, default = None,
        help = "Set the host. | 设置主机 (default: None) \n仅集群模式下生效"
    )

    group = parser.add_mutually_exclusive_group()
    group.add_argument(
        "--install", action = "store_true",
        help = "Install the WhaleStudio. | 安装 WhaleStudio"
    )
    group.add_argument(
        "--uninstall", action = "store_true",
        help = "Uninstall the WhaleStudio. | 卸载 WhaleStudio"
    )

    group.add_argument(
        "--start", nargs = '?', const = "all", help = "Start the WhaleStudio. | 启动 WhaleStudio"
    )
    group.add_argument(
        "--stop", nargs = '?', const = "all", help = "Stop the WhaleStudio. | 停止 WhaleStudio"
    )
    group.add_argument(
        "--restart", nargs = '?', const = "all", help = "Restart the WhaleStudio. | 重启 WhaleStudio"
    )

    group.add_argument(
        "--status", nargs = '?', const = "all", help = "Get the status of the WhaleStudio. | 获取 WhaleStudio 状态"
    )
    group.add_argument(
        "--logs", nargs = '?', const = "api", help = "Get the logs of the WhaleStudio. | 获取 WhaleStudio 日志",
        choices = [
            "api", "master", "worker", "alert", "whaletunnel"
        ]
    )
    group.add_argument(
        "--config_update", action = "store_true",
        help = "Update the WhaleStudio configuration. | 更新 WhaleStudio 配置"
    )
    group.add_argument(
        "--db_init", nargs = "?", const = "None", help = "Initialize the WhaleStudio database. | 初始化 WhaleStudio 数据库"
    )
    group.add_argument(
        "--db_upgrade", nargs = "?", const = "None", help = "Upgrade the WhaleStudio database. | 升级 WhaleStudio 数据库"
    )
    group.add_argument(
        "--clean_packages", action = "store_true",
        help = "Clean the historical WhaleStudio packages. | 清理历史 WhaleStudio 包"
    )

    group.add_argument(
        "--pre_check", action = "store_true",
        help = "Perform the prerequisite check. | 执行前置检查"
    )

    # 版本回退
    group.add_argument(
        "--rollback", action = "store_true",
        help = "Rollback the WhaleStudio to the previous version. | 回滚 WhaleStudio 到上一版本"
    )
    # group.add_argument(
    #     "--pre_check_help", action = "store_true",
    #     help = "Show the help information of the prerequisite check. | 显示前置检查帮助信息"
    # )

    args = parser.parse_args()
    setup_logging(level = args.log_level)
    from app.common.logging_utils import error_handler, info_handler

    root_user, user_name = startup_user()

    if not deployment_config.root_user and root_user:
        error_handler(message = "Please do not start the script as root user!!!")
        sys.exit(1)

    deploy_args = {
        "install": args.install,
        "uninstall": args.uninstall,
        "start": args.start,
        "stop": args.stop,
        "restart": args.restart,
        "status": args.status,
        "logs": args.logs,
        "config_update": args.config_update,
        "db_init": args.db_init,
        "db_upgrade": args.db_upgrade,
        "clean_packages": args.clean_packages,
        "pre_check": args.pre_check,
        "rollback": args.rollback,
    }

    # 设置一个环境变量，用于区分是本地启动还是集群启动
    os.environ['WHALETUNNEL_CONFIG_PATH'] = os.path.join(
        CURRENT_DIRECTORY, "config", "seatunnel.yaml"
    )
    os.environ['DEPLOYMENT_CONFIG_PATH'] = os.path.join(
        CURRENT_DIRECTORY, "config", "deployment.yaml"
    )
    os.environ['ENVIRONMENT_CONFIG_PATH'] = os.path.join(
        CURRENT_DIRECTORY, "config", "environment.yaml"
    )
    os.environ['LOGS_PATH'] = os.path.join(CURRENT_DIRECTORY, "logs")
    os.environ['OTHER_FILES_PATH'] = os.path.join(CURRENT_DIRECTORY, "config", "other_files")
    if all(value is None or not value for value in deploy_args.values()):
        parser.print_help()
        sys.exit(1)

    try:
        deploy_whale_studio(deploy_args, args.host)
    except Exception as e:
        error_handler(message = str(e))
        sys.exit(1)
    except KeyboardInterrupt:
        info_handler(message = "KeyboardInterrupt")
        sys.exit(0)


if __name__ == '__main__':
    main()
