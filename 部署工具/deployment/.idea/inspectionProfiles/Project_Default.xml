<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="ComposeUnknownKeys" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="HttpClientUnresolvedAuthId" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="PyArgumentListInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyCallingNonCallableInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="37">
            <item index="0" class="java.lang.String" itemvalue="whale-python-color" />
            <item index="1" class="java.lang.String" itemvalue="PyYAML" />
            <item index="2" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="3" class="java.lang.String" itemvalue="scramp" />
            <item index="4" class="java.lang.String" itemvalue="cffi" />
            <item index="5" class="java.lang.String" itemvalue="psycopg2-binary" />
            <item index="6" class="java.lang.String" itemvalue="pg8000" />
            <item index="7" class="java.lang.String" itemvalue="pycparser" />
            <item index="8" class="java.lang.String" itemvalue="requests" />
            <item index="9" class="java.lang.String" itemvalue="PyNaCl" />
            <item index="10" class="java.lang.String" itemvalue="kazoo" />
            <item index="11" class="java.lang.String" itemvalue="certifi" />
            <item index="12" class="java.lang.String" itemvalue="urllib3" />
            <item index="13" class="java.lang.String" itemvalue="six" />
            <item index="14" class="java.lang.String" itemvalue="asn1crypto" />
            <item index="15" class="java.lang.String" itemvalue="cryptography" />
            <item index="16" class="java.lang.String" itemvalue="bcrypt" />
            <item index="17" class="java.lang.String" itemvalue="tqdm" />
            <item index="18" class="java.lang.String" itemvalue="paramiko" />
            <item index="19" class="java.lang.String" itemvalue="thriftpy2" />
            <item index="20" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="21" class="java.lang.String" itemvalue="PyMySQL" />
            <item index="22" class="java.lang.String" itemvalue="ply" />
            <item index="23" class="java.lang.String" itemvalue="idna" />
            <item index="24" class="java.lang.String" itemvalue="prompt_toolkit" />
            <item index="25" class="java.lang.String" itemvalue="ruamel.yaml" />
            <item index="26" class="java.lang.String" itemvalue="rich" />
            <item index="27" class="java.lang.String" itemvalue="wcwidth" />
            <item index="28" class="java.lang.String" itemvalue="loguru" />
            <item index="29" class="java.lang.String" itemvalue="markdown-it-py" />
            <item index="30" class="java.lang.String" itemvalue="plumbum" />
            <item index="31" class="java.lang.String" itemvalue="prettytable" />
            <item index="32" class="java.lang.String" itemvalue="Pygments" />
            <item index="33" class="java.lang.String" itemvalue="ruamel.yaml.clib" />
            <item index="34" class="java.lang.String" itemvalue="mdurl" />
            <item index="35" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="36" class="java.lang.String" itemvalue="rpyc" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8Inspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="E501" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N802" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>