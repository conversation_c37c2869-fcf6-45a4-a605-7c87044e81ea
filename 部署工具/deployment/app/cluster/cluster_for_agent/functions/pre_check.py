#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : pre_check.py
# @Time    : 2025/06/05 15:36
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0
import json
import os
import sys
import traceback
import time
from datetime import datetime

from app.common.logging_utils import debug_handler, warning_handler, info_handler, error_handler
from app.config.deployment_config import deployment_config, load_env_command

from app.cluster.cluster_for_agent.tools.node_connect import NodeAgentConnect

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class AgentClusterPreCheck:
    """
    This class is used to check the environment before starting the agent cluster.
    """

    def __init__(self, host, result_dict, agent_client):
        self.host = host
        self.result_dict = result_dict
        self.agent_client = agent_client

    def run(self):
        """
        This method is used to check the environment before starting the agent cluster.
        :return:
        """
        warning_handler(message = f"Start to check Agent cluster {self.host} pre-check. Please wait...")
        start_time = time.time()
        pre_check_result = json.loads(
            self.agent_client.root.operation(
                "pre_check",
                root_start = deployment_config.root_user,
                start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                deployment_dir = deployment_config.deployment_dir,
                load_env_command = load_env_command()
            )
        )
        end_time = time.time()
        pre_check_status = pre_check_result.get("status", True)
        result_message = pre_check_result.get("result", [])
        failed_message = pre_check_result.get("failed_list", "")
        pre_checK_cost_time = end_time - start_time
        if pre_checK_cost_time > 60:
            self.result_dict[self.host] += [
                f"Agent cluster pre-check time consumption {pre_checK_cost_time / 60:.2f} minutes."
            ]
        else:
            self.result_dict[self.host] += [
                f"Agent cluster pre-check time consumption {pre_checK_cost_time:.2f} seconds."
            ]
        if result_message:
            # info_handler(message = f"Host: {self.host} pre-check result:")
            for message in result_message:
                info_handler(message = f"   - {message}")
        if not pre_check_status:
            return False, failed_message
        return True, ""


def pre_check_agent_node(host, *args, **kwargs):
    """
    This function is used to start the agent cluster.
    :return:
    """
    result_dict = kwargs.get("result_dict", {})
    agent_connect_start_time = time.time()
    node_agent_connect = NodeAgentConnect(node_ip = host)
    agent_client, agent_connect_result = node_agent_connect.get_node_conn()
    if not agent_client:
        return False, agent_connect_result
    result_dict[host] = []
    agent_connect_end_time = time.time()
    agent_connect_time_consumption = agent_connect_end_time - agent_connect_start_time
    if agent_connect_time_consumption > 60:
        result_dict[host] += [
            f"Agent connect time consumption {agent_connect_time_consumption / 60:.2f} minutes."
        ]
    else:
        result_dict[host] += [
            f"Agent connect time consumption {agent_connect_time_consumption:.2f} seconds."
        ]
    try:
        return AgentClusterPreCheck(
            host, result_dict, agent_client
        ).run()
    except Exception as e:
        error_handler(message = f"Host: {host} Failed to check Agent cluster pre-check: {e}")
        debug_handler(message = traceback.format_exc())
        return False, "Failed to check Agent cluster pre-check."
