#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : whaleStudio_rollback.py
# @Time    : 2025/06/17 11:40
# <AUTHOR> chenyi<PERSON>i
# @Version : 1.0

import os
import sys
import time
from app.config.deployment_config import deployment_config
from app.cluster.cluster_for_agent.tools.node_connect import NodeAgentConnect

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


def rollback_whale_studio(host, *args, **kwargs):
    result_dict = kwargs.get("result_dict", {})
    agent_connect_start_time = time.time()
    node_agent_connect = NodeAgentConnect(node_ip = host)
    agent_client, agent_connect_result = node_agent_connect.get_node_conn()
    if not agent_client:
        return False, agent_connect_result
    result_dict[host] = []
    kwargs["result_dict"] = result_dict
    agent_connect_end_time = time.time()
    agent_connect_time_consumption = agent_connect_end_time - agent_connect_start_time
    if agent_connect_time_consumption > 60:
        result_dict[host] += [
            f"Agent connect time consumption {agent_connect_time_consumption / 60:.2f} minutes."
        ]
    else:
        result_dict[host] += [
            f"Agent connect time consumption {agent_connect_time_consumption:.2f} seconds."
        ]

    rollback_start_time = time.time()
    rollback_status, rollback_result = agent_client.root.rollback_whale_studio(deployment_config.deployment_dir)
    if not rollback_status:
        return False, rollback_result

    rollback_end_time = time.time()
    rollback_time_consumption = rollback_end_time - rollback_start_time
    if rollback_time_consumption > 60:
        result_dict[host] += [
            f"Rollback time consumption {rollback_time_consumption / 60:.2f} minutes."
        ]
    else:
        result_dict[host] += [
            f"Rollback time consumption {rollback_time_consumption:.2f} seconds."
        ]
    return True, f"Host: {host} Rollback succeeded."
