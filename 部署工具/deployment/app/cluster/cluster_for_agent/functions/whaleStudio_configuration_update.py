#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : whaleStudio_configuration_update.py
# @Time    : 2025/06/10 16:15
# <AUTHOR> chenyi<PERSON>i
# @Version : 1.0
import base64
import os
import sys
import time
import json
import traceback
from app.config.deployment_config import deployment_config
from app.config.whaleTunnel_config import whale_tunnel_config
from app.config.scheduler_config import other_files_to_sync
from app.common.package import get_package_md5sum
from app.common.logging_utils import error_handler, debug_handler
from app.common.utils import service_jvm_stack_size, service_jvm_young_generation_size
from app.cluster.cluster_for_agent.tools.node_connect import NodeAgentConnect
from app.cluster.config.cluster_node_config import scheduler_setup_config, get_services_by_ip, get_service_jvm_size, \
    get_ip_by_service

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class WhaleStudioConfigurationUpdate:
    """
    This class is used to update the configuration file of whale studio.
    """

    def __init__(self, host, agent_client, **kwargs):
        self.host = host
        self.agent_client = agent_client
        self.kwargs = kwargs
        self.kwargs["host"] = host
        self.result_dict = kwargs.get("result_dict", {})
        whale_scheduler_current_path = os.path.join(
            deployment_config.deployment_dir, "current", "whalestudio"
        )

        # self._whale_tunnel_config = kwargs.get("whale_tunnel_config", whale_tunnel_config())
        self.scheduler_service_config_path = {
            "api": os.path.join(
                whale_scheduler_current_path, "api-server", "conf"
            ),
            "master": os.path.join(
                whale_scheduler_current_path, "master-server", "conf"
            ),
            "worker": os.path.join(
                whale_scheduler_current_path, "worker-server", "conf"
            ),
            "alert": os.path.join(
                whale_scheduler_current_path, "alert-server", "conf"
            )
        }

        self.scheduler_config_path = os.path.join(
            whale_scheduler_current_path, "bin", "env", "whalescheduler_env.sh"
        )
        self.whale_tunnel_config_path = os.path.join(
            deployment_config.deployment_dir, "current", "whaletunnel", "config"
        )

    def run(self):
        # 配置文件修改请求内容
        self.modify_config_request = {
            "scheduler_setup_config": {
                "path": self.scheduler_config_path,
                "type": "text",
                "content": scheduler_setup_config(ip = self.host),
                "description": "update scheduler setup config"
            }
        }
        #
        generate_request_body_tasks = [
            self.scheduler_jvm_config
        ]
        if deployment_config.deploy_whaletunnel:
            generate_request_body_tasks += [
                self.whale_tunnel_config,
                self.whaleTunnel_jvm_config,
                self.hazelcast_config,
                self.hazelcast_client_config
            ]
        for generate_request_body_task in generate_request_body_tasks:
            try:
                generate_request_body_task()
            except Exception as e:
                error_handler(message = f"Host {self.host} update configuration file failed.")
                debug_handler(message = traceback.format_exc())
                return False, f"Failed to modify configuration file Error: {e}"
        # 发送配置文件修改请求
        modify_remote_config_result = json.loads(
            self.agent_client.root.operation(
                operation = "modify_config",
                config_request = json.dumps(self.modify_config_request),
                **self.kwargs
            )
        )

        debug_handler(message = f"Host {self.host} modify configuration file result: {modify_remote_config_result}")
        if not modify_remote_config_result[0]:
            return False, modify_remote_config_result[1]
        # 判断是否有需要同步的文件
        other_files = other_files_to_sync()
        if other_files:
            start_time = time.time()
            other_files_request = {}
            # print("Start to update other files.")
            for file_path in other_files:
                file_name = os.path.basename(file_path)
                with open(file_path, "rb") as f:
                    file_content = base64.b64encode(f.read())
                    file_md5sum = get_package_md5sum(
                        package_path = file_path
                    )
                    other_files_request[file_name] = {
                        "file_content": file_content.decode(),
                        "md5sum": file_md5sum,
                        "description": f"update {file_name} file",
                        "service_path": []
                    }
                for service_name in get_services_by_ip(ip = self.host):
                    service_config_path = self.scheduler_service_config_path.get(service_name)
                    if not service_config_path:
                        continue
                    other_files_request[file_name]["service_path"].append(
                        service_config_path
                    )
                debug_handler(message = f"Host {self.host} update {file_name} file: {file_content.decode()}")
            sync_other_files_status, sync_other_files_result = self.agent_client.root.upload_file(
                json.dumps(other_files_request)
            )
            # print(sync_other_files_result)
            # print(sync_other_files_status)
            if not sync_other_files_status:
                error_handler(message = f"Host {self.host} update other files failed.")
                return False, f"Failed to update other files: {sync_other_files_result}"
            end_time = time.time()
            debug_handler(
                message = f"Host {self.host} update configuration file successfully, cost time: {end_time - start_time}s"
            )
            sync_other_files_time_const = end_time - start_time
            if sync_other_files_time_const > 60:
                self.result_dict[self.host] += [
                    f"Update other files cost time: {sync_other_files_time_const / 60:.2f} minutes."
                ]
            else:
                self.result_dict[self.host] += [
                    f"Update other files cost time: {sync_other_files_time_const:.2f} seconds."
                ]
        return True, "Update configuration file successfully."

    def whale_tunnel_config(self):
        """
        update the whale tunnel configuration.
        :return:
        """
        self.modify_config_request[
            "whale_tunnel_config"
        ] = {
            "path": os.path.join(self.whale_tunnel_config_path, "seatunnel.yaml"),
            "type": "yaml",
            "content": whale_tunnel_config(),
            "description": "update whale tunnel config"
        }

    def hazelcast_config(self):
        """
        update the hazelcast configuration.
        :return:
        """
        hazelcast_config_path = os.path.join(self.whale_tunnel_config_path, "hazelcast.yaml")
        hazelcast_config_result = self._read_remote_file(
            file_path = hazelcast_config_path,
            file_type = "yaml"
        )
        if not hazelcast_config_result:
            error_handler(message = f"Read Hazelcast config file failed.")
            return False
        new_hazelcast_config = hazelcast_config_result.copy()
        new_hazelcast_config[
            "hazelcast"
        ][
            "network"
        ][
            "join"
        ][
            "tcp-ip"
        ][
            "member-list"
        ] = get_ip_by_service(
            service_name = "whaletunnel",
        )
        debug_handler(message = f"Host {self.host} update hazelcast config: {new_hazelcast_config}")
        self.modify_config_request[
            "hazelcast_config"
        ] = {
            "path": hazelcast_config_path,
            "type": "yaml",
            "content": new_hazelcast_config,
            "description": "update hazelcast config"
        }

    def hazelcast_client_config(self):
        """
        update the hazelcast client configuration.
        :return:
        """
        hazelcast_client_config_path = os.path.join(self.whale_tunnel_config_path, "hazelcast-client.yaml")
        hazelcast_client_config_result = self._read_remote_file(
            file_path = hazelcast_client_config_path,
            file_type = "yaml"
        )
        if not hazelcast_client_config_result:
            error_handler(message = f"Read Hazelcast client config file failed.")
            return False
        new_hazelcast_client_config = hazelcast_client_config_result.copy()
        new_hazelcast_client_config[
            "hazelcast-client"
        ][
            "network"
        ][
            "cluster-members"
        ] = [
            f"{ip}:5801" for ip in get_ip_by_service(
                service_name = "whaletunnel",
            )
        ]
        debug_handler(message = f"Host {self.host} update hazelcast client config: {new_hazelcast_client_config}")
        self.modify_config_request[
            "hazelcast_client_config"
        ] = {
            "path": hazelcast_client_config_path,
            "type": "yaml",
            "content": new_hazelcast_client_config,
            "description": "update hazelcast client config"
        }

    def whaleTunnel_jvm_config(self):
        """
        update the whale tunnel configuration.
        :return:
        """
        whale_tunnel_jvm_file_path = os.path.join(
            self.whale_tunnel_config_path, "jvm_options"
        )
        whale_tunnel_jvm_config_result = self._read_remote_file(
            file_path = whale_tunnel_jvm_file_path,
            file_type = "txt"

        )
        if not whale_tunnel_jvm_config_result:
            error_handler(message = f"Read Whale Tunnel JVM config file failed.")
            return False
        current_jvm_config = [
            line.strip() for line in whale_tunnel_jvm_config_result if
            line.strip() and not line.strip().startswith(("-Xm", "-XX:ConcGCThreads"))
        ]
        whale_tunnel_jvm_size = get_service_jvm_size(ip = self.host, service_name = "whaletunnel")
        current_jvm_config += [
            f"-Xms{service_jvm_stack_size(jvm_stack_size = whale_tunnel_jvm_size)}",
            f"-Xmx{service_jvm_stack_size(jvm_stack_size = whale_tunnel_jvm_size)}",
        ]
        remote_cpu_cores = json.loads(
            self.agent_client.root.operation(
                operation = "get_cpu_cores"
            )
        )
        if not remote_cpu_cores.get("status") or remote_cpu_cores.get("cpu_cores") is None:
            cpu_cores = 8
        else:
            cpu_cores = remote_cpu_cores.get("cpu_cores")
        if cpu_cores > 8:
            current_jvm_config.append(
                "-XX:ConcGCThreads=6"
            )
        else:
            current_jvm_config.append(
                f"-XX:ConcGCThreads={max(1, int(cpu_cores * 5 / 8))}"
            )
        debug_handler(message = f"Host {self.host} update whale tunnel JVM config: {current_jvm_config}")
        self.modify_config_request[
            "whale_tunnel_jvm_config"
        ] = {
            "path": whale_tunnel_jvm_file_path,
            "type": "text",
            "content": "\n".join(current_jvm_config),
            "description": "update whale tunnel jvm config"
        }

    def scheduler_jvm_config(self):
        """
        update the scheduler jvm configuration.
        :return:
        """
        # 1. 去读取远端的配置文件
        scheduler_service_list = [
            service for service in get_services_by_ip(ip = self.host) if service != "whaletunnel"
        ]
        for service in scheduler_service_list:
            service_jvm_size = get_service_jvm_size(ip = self.host, service_name = service)
            service_jvm_config_path = os.path.join(
                os.path.dirname(
                    self.scheduler_service_config_path.get(
                        service
                    )
                ), "bin", "jvm_args_env.sh"
            )
            service_jvm_config_result = self._read_remote_file(
                file_path = service_jvm_config_path,
                file_type = "txt"
            )
            if not service_jvm_config_result:
                error_handler(message = f"Read Service {service} JVM config file failed.")
                continue
            new_service_jvm_config_content = [line.strip() for line in service_jvm_config_result if
                                              line.strip() and not line.startswith("-Xm")]
            new_service_jvm_config_content += [
                f"-Xms{service_jvm_stack_size(jvm_stack_size = service_jvm_size)}",
                f"-Xmx{service_jvm_stack_size(jvm_stack_size = service_jvm_size)}",
                f"-Xmn{service_jvm_young_generation_size(jvm_stack_size = service_jvm_size)}"
            ]
            self.modify_config_request[
                f"service_{service}_jvm_config"
            ] = {
                "path": service_jvm_config_path,
                "type": "text",
                "content": "\n".join(new_service_jvm_config_content),
                "description": f"update service {service} jvm config"
            }

    def _read_remote_file(self, file_path, file_type):
        """

        :param file_path:
        :param file_type:
        :return:
        """
        read_file_result = json.loads(
            self.agent_client.root.operation(
                operation = "read_file",
                **{
                    "file_path": file_path,
                    "file_type": file_type
                }
            )
        )
        if not read_file_result.get("status"):
            return None
        return read_file_result.get("content")


def update_configuration_file(
        host, *args, **kwargs
):
    result_dict = kwargs.get("result_dict", {})
    agent_connect_start_time = time.time()
    node_agent_connect = NodeAgentConnect(node_ip = host)
    agent_client, agent_connect_result = node_agent_connect.get_node_conn()
    if not agent_client:
        return False, agent_connect_result
    result_dict[host] = []
    agent_connect_end_time = time.time()
    agent_connect_time_consumption = agent_connect_end_time - agent_connect_start_time
    if agent_connect_time_consumption > 60:
        result_dict[host] += [
            f"Agent connect time consumption {agent_connect_time_consumption / 60:.2f} minutes."
        ]
    else:
        result_dict[host] += [
            f"Agent connect time consumption {agent_connect_time_consumption:.2f} seconds."
        ]
    try:
        return WhaleStudioConfigurationUpdate(
            host = host,
            agent_client = agent_client,
            **kwargs
        ).run()
    except Exception as e:
        error_handler(message = f"Host {host} update configuration file failed.")
        debug_handler(message = traceback.format_exc())
        return False, f"Failed to modify configuration file Error: {e}"
