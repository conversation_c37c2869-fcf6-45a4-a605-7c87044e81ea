#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : whaleStudio_install.py
# @Time    : 2025/06/09 19:27
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys
import time
import json
import traceback
from app.config.deployment_config import deployment_config
from app.config.whaleTunnel_config import whale_tunnel_config
from app.common.logging_utils import error_handler, debug_handler, warning_handler, title_handler

from app.cluster.cluster_for_agent.tools.node_connect import NodeAgentConnect
from app.cluster.cluster_for_agent.functions.pre_check import AgentClusterPreCheck
from app.cluster.cluster_for_agent.functions.whaleStudio_configuration_update import WhaleStudioConfigurationUpdate

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class WhaleStudioInstaller:
    """Whale Studio Installer. | Whale Studio 安装器"""

    def __init__(self, host, agent_client, **kwargs):
        self.host = host
        self.result_dict = kwargs.get("result_dict", {})
        self.agent_client = agent_client
        self.kwargs = kwargs
        self.kwargs[
            "download_url"
        ] = f"http://{deployment_config.local_ip}:{deployment_config.download_port}"

    def run(self):
        title_handler(message = f"Installing Whale Studio on {self.host}")
        tasks = [
            self.install_whale_studio,
            self.update_whale_studio_config,
        ]
        if not deployment_config.skip_pre_check:
            tasks.insert(0, self.pre_check)
        for task in tasks:
            status, result = task()
            if not status:
                return False, result
        return True, "Whale Studio Installer"

    def install_whale_studio(self):
        warning_handler(message = "Start to install Whale Studio. Please wait for a moment.")
        whales_studio_install_result = json.loads(
            self.agent_client.root.operation(
                "install",
                **self.kwargs
            )
        )
        if not whales_studio_install_result.get("status"):
            return False, whales_studio_install_result.get("failed_message", "")
        self.result_dict[self.host] += whales_studio_install_result.get("result", "")

        return True, "Whale Studio Installer"

    def pre_check(self):
        pre_check_start_time = time.time()
        # 1. 先进行环境检查
        pre_check_status, pre_check_result = AgentClusterPreCheck(
            host = self.host, result_dict = self.result_dict, agent_client = self.agent_client
        ).run()

        if not pre_check_status:
            return False, pre_check_result
        pre_check_time_consumption = time.time() - pre_check_start_time
        if pre_check_time_consumption > 60:
            warning_handler(
                message = f"Pre-check time consumption is longer than {pre_check_time_consumption / 60:.2f} minutes."
            )
        else:
            warning_handler(message = f"Pre-check time consumption is {pre_check_time_consumption:.2f} seconds.")
        return True, "Pre-check"

    def update_whale_studio_config(self):
        update_config_status, update_config_result = WhaleStudioConfigurationUpdate(
            host = self.host, agent_client = self.agent_client, **self.kwargs
        ).run()
        if not update_config_status:
            return False, update_config_result

        return True, "Whale Studio Configuration Update"


def install_whale_studio(host, *args, **kwargs):
    """Install Whale Studio. | 安装 Whale Studio"""
    result_dict = kwargs.get("result_dict", {})
    agent_connect_start_time = time.time()
    node_agent_connect = NodeAgentConnect(node_ip = host)
    agent_client, agent_connect_result = node_agent_connect.get_node_conn()
    if not agent_client:
        return False, agent_connect_result
    result_dict[host] = []
    kwargs["result_dict"] = result_dict
    agent_connect_end_time = time.time()
    agent_connect_time_consumption = agent_connect_end_time - agent_connect_start_time
    if agent_connect_time_consumption > 60:
        result_dict[host] += [
            f"Agent connect time consumption {agent_connect_time_consumption / 60:.2f} minutes."
        ]
    else:
        result_dict[host] += [
            f"Agent connect time consumption {agent_connect_time_consumption:.2f} seconds."
        ]
    try:
        return WhaleStudioInstaller(
            host, agent_client, **kwargs
        ).run()
    except Exception as e:
        error_handler(message = f"Failed to install Whale Studio on {host}. Error: {e}")
        debug_handler(message = traceback.format_exc())
        return False, f"Failed to install Whale Studio on {host}."
