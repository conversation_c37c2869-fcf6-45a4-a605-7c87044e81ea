#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : crypto_utils.py
# @Time    : 2025/06/09 11:22
# <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import sys
import hashlib
import hmac

from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class SecureChannel:
    """
    This class is used to secure the communication channel between the agent and the server. | 该类用于在Agent和服务器之间建立安全通信通道。
    """
    def __init__(self, master_key):
        """
        Initialize the secure channel with the given master key. | 初始化安全通道并传入主密钥。
        :param master_key:
        """
        if isinstance(master_key, str):
            master_key = master_key.encode()
        self.master_key = master_key

    def derive_session_key(self, salt = None):
        """
        Derive the session key from the master key and the given salt. | 从主密钥和给定的盐派生会话密钥。
        :param salt:
        :return:
        """
        if salt is None:
            salt = os.urandom(16)  # Generate a random salt if not provided. | 如果没有提供盐，则生成一个随机盐。

        derived = hashlib.pbkdf2_hmac(
            hash_name = 'sha256',
            password = self.master_key,  # 主密钥
            salt = salt,  # 盐
            iterations = 100000,  # 迭代次数
            dklen = 32 * 3  # 生成 96 位的会话密钥
        )
        return {
            'enc_key': derived[:32],
            'iv_key': derived[32:64],
            'auth_key': derived[64:],
            'salt': salt
        }

    def encrypt_and_sign(self, plaintext, session_key):
        """加密数据并生成签名"""
        # 生成随机IV
        iv = os.urandom(16)

        # AES-CBC加密
        cipher = AES.new(session_key['enc_key'], AES.MODE_CBC, iv)
        padded = pad(plaintext.encode(), AES.block_size)
        ciphertext = cipher.encrypt(padded)

        # 计算HMAC签名（数据 + IV）
        to_sign = iv + ciphertext
        signature = hmac.new(session_key['auth_key'], to_sign, 'sha256').digest()

        # 组装完整消息: IV + ciphertext + signature
        return iv + ciphertext + signature

    def decrypt_and_verify(self, encrypted_data, session_key):
        """解密数据并验证签名"""
        # 拆分消息: IV(16) + ciphertext + signature(32)
        iv = encrypted_data[:16]
        signature = encrypted_data[-32:]
        ciphertext = encrypted_data[16:-32]

        # 验证签名
        to_verify = iv + ciphertext
        expected_signature = hmac.new(session_key['auth_key'], to_verify, 'sha256').digest()

        if not hmac.compare_digest(signature, expected_signature):
            raise ValueError("无效签名，可能数据被篡改!")

        # AES-CBC解密
        cipher = AES.new(session_key['enc_key'], AES.MODE_CBC, iv)
        padded = cipher.decrypt(ciphertext)
        plaintext = unpad(padded, AES.block_size).decode()

        return plaintext
