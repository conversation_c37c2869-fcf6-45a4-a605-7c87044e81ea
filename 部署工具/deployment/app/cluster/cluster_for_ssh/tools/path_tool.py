#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : path_tool.py
# @Time    : 2025/06/05 17:04
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys
import traceback

from app.common.logging_utils import debug_handler

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class PathTool:
    """
    This class is used to get the path of the current script.
    """

    def __init__(self, sftp_client):
        """
        Initialize the PathTool class with an SFTP client.
        :param sftp_client:
        """
        self.sftp_client = sftp_client

    def is_directory_exist(self, directory_path):
        """
        Check if a directory exists.
        :param directory_path:
        :return:
        """
        try:
            directory_path_stat = self.sftp_client.stat(directory_path)
            if directory_path_stat.st_mode & 0o170000 == 0o040000:
                # If the mode indicates a directory | 如果模式表示一个目录
                return True
            else:
                return False

        except IOError:
            return False

    def create_directory(self, directory_path):
        """
        Create a directory if it does not exist.
        :param directory_path:
        :return:
        """
        # 待创建目录
        to_create_directory = ""
        for path in directory_path.split("/"):
            to_create_directory += f"/{path}"
            if not self.is_directory_exist(directory_path = to_create_directory):
                try:
                    self.sftp_client.mkdir(to_create_directory)
                except IOError:
                    return False, f"Failed to create directory: {to_create_directory}"
        return True, f"Directory created: {to_create_directory}"

    def sub_directory_list(self, directory_path):
        """
        Get the list of subdirectories in a directory.
        :param directory_path:
        :return:
        """
        try:
            subdirectories = []
            for entry in self.sftp_client.listdir_attr(directory_path):
                if entry.st_mode & 0o170000 == 0o040000:  # Check if it is a directory
                    subdirectories.append(entry.filename)
            return subdirectories
        except IOError as e:
            debug_handler(message = f"Failed to get subdirectories in {directory_path}: {e}")
            return None
        except Exception as e:
            debug_handler(message = f"Failed to get subdirectories in {directory_path}: {e}")
            debug_handler(message = traceback.format_exc())
            return None

    def sub_file_list(self, directory_path):
        """
        Get the list of files in a directory.
        :param directory_path:
        :return:
        """
        try:
            files = []
            for entry in self.sftp_client.listdir_attr(directory_path):
                if entry.st_mode & 0o170000 != 0o040000:  # Check if it is a file
                    files.append(entry.filename)
            return files
        except IOError as e:
            debug_handler(message = f"Failed to get files in {directory_path}: {e}")
            return None
        except Exception as e:
            debug_handler(message = f"Failed to get files in {directory_path}: {e}")
            debug_handler(message = traceback.format_exc())
            return None

    def get_symlink(self, symlink_path):
        """
        Get the target of a symbolic link.
        :param symlink_path:
        :return:
        """
        try:
            sub_directories = self.sftp_client.listdir(symlink_path)
            if not sub_directories:
                return None

            symlink_info = {}
            for sub_directory in sub_directories:
                symlink_info[os.path.join(
                    symlink_path, sub_directory
                )] = self.sftp_client.readlink(
                    os.path.join(symlink_path, sub_directory)
                )
            return symlink_info
        except IOError as e:
            debug_handler(message = f"Failed to get symlink target in {symlink_path}: {e}")
            return None
        except Exception as e:
            debug_handler(message = f"Failed to get symlink target in {symlink_path}: {e}")
            debug_handler(message = traceback.format_exc())
            return None

    def delete_symlink(self, symlink_paths):
        """
        Delete a symbolic link.
        :param symlink_paths:
        :return:
        """
        for symlink_path in symlink_paths:
            try:
                if self.sftp_client.lstat(symlink_path).st_mode & 0o170000 == 0o120000:  # Check if it is a symlink
                    self.sftp_client.unlink(symlink_path)
                else:
                    debug_handler(message = f"{symlink_path} is not a symlink.")
                    return False, f"{symlink_path} is not a symlink."
            except IOError as e:
                debug_handler(message = f"Failed to delete symlink {symlink_path}: {e}")
                return False, f"Failed to delete symlink {symlink_path}: {e}"
            except Exception as e:
                debug_handler(message = f"Failed to delete symlink {symlink_path}: {e}")
                debug_handler(message = traceback.format_exc())
                return False, f"Failed to delete symlink {symlink_path}: {e}"
        return True, "Symlinks deleted successfully."

    def create_symlink(self, symlink_dict):
        """
        Create symbolic links based on the provided dictionary.
        :param symlink_dict:
        :return:
        """
        for source_path, target_path in symlink_dict.items():
            try:
                self.sftp_client.symlink(target_path, source_path)
                debug_handler(message = f"Symlink created {target_path} -> {source_path} successfully.")
            except IOError as e:
                debug_handler(message = f"Failed to create symlink {source_path}: {e}")
                return False, f"Failed to create symlink {source_path}: {e}"
            except Exception as e:
                debug_handler(message = f"Failed to create symlink {source_path}: {e}")
                debug_handler(message = traceback.format_exc())
                return False, f"Failed to create symlink {source_path}: {e}"
        return True, "Symlinks created successfully."

    def delete_directory(self, directory_path):
        """
        Delete a directory. | 删除目录

        :param directory_path:
        :return:
        """
        try:
            self.sftp_client.rmdir(directory_path)
            return True, f"Directory deleted: {directory_path}"
        except IOError as e:
            debug_handler(message = f"Failed to delete directory {directory_path}: {e}")
            return False, f"Failed to delete directory {directory_path}: {e}"
        except Exception as e:
            debug_handler(message = f"Failed to delete directory {directory_path}: {e}")
            debug_handler(message = traceback.format_exc())
            return False, f"Failed to delete directory {directory_path}: {e}"
