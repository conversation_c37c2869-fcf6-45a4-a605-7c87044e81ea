#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : command_execution.py
# @Time    : 2025/06/05 16:30
# <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import sys

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


def execute_command(ssh_client, command):
    try:
        stdin, stdout, stderr = ssh_client.exec_command(command)
        exit_status = stdout.channel.recv_exit_status()  # Wait for command to complete | 等待命令完成
        stdout_str = stdout.read().decode('utf-8')  # Get the standard output of the command | 获取命令的标准输出
        stderr_str = stderr.read().decode('utf-8')  # Get the standard error of the command | 获取命令的标准错误
        return exit_status, stdout_str, stderr_str
    except Exception as e:
        return -1, '', str(e)

