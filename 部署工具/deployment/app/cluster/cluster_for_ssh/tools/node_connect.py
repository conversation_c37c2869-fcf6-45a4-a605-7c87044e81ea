#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : node_connect.py
# @Time    : 2025/06/05 15:47
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys
import paramiko

from app.config.deployment_config import deployment_config

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


def node_connect_config(node_ip):
    """
    Get the configuration for connecting to a node. | 获取连接节点的配置
    :return:
    """
    node_config = deployment_config.cluster_nodes.get(node_ip)
    unified_login_configuration = deployment_config.unified_deployment_user
    if not node_config and not unified_login_configuration:
        return None
    if not node_config or node_config.get(
            "use_deployment_user", False
    ):
        username = unified_login_configuration.get("username", "whalestudio")
        port = unified_login_configuration.get("port", 22)
        timeout = unified_login_configuration.get("timeout", 60)
        if unified_login_configuration.get("password_free", False):
            password = None
            key_file = unified_login_configuration.get(
                "public_key"
            )
        else:
            key_file = None
            password = unified_login_configuration.get("password", "QWer12#$")

    else:
        username = node_config.get("username", "whalestudio")
        port = node_config.get("port", 22)
        timeout = node_config.get("timeout", 60)
        if node_config.get("password_free", False):
            password = None
            key_file = node_config.get("public_key")
        else:
            key_file = None
            password = node_config.get("password", "QWer12#$")
    return {
        "hostname": node_ip,
        "username": username,
        "password": password,
        "port": port,
        "timeout": timeout,
        "key_filename": key_file,
    }


def node_connect(node_ip):
    """
    Connect to a node via SSH. | 通过SSH连接到节点
    :param node_ip: The IP address of the node to connect to. | 要连接的节点的IP地址
    :return:
    """
    node_login_config = node_connect_config(node_ip)

    if node_login_config.get("username") == "root" and not deployment_config.root_user:
        return None, None, "Root user is not allowed in this deployment."

    if not node_login_config:
        return None, None, "No login configuration found."
    try:
        ssh_client = paramiko.SSHClient()
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh_client.connect(
            **node_login_config
        )
        sftp_client = ssh_client.open_sftp()
        return ssh_client, sftp_client, None
    except Exception as e:
        return None, None, f"Host: Connection failed. Reason: {str(e)}"
