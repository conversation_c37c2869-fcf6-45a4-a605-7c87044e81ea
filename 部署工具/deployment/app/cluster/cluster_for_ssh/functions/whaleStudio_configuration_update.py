#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : whaleStudio_configuration_update.py
# @Time    : 2025/06/06 14:53
# <AUTHOR> chenyi<PERSON>i
# @Version : 1.0

import os
import sys
import time
import traceback

from app.config.deployment_config import deployment_config
from app.config.scheduler_config import other_files_to_sync
from app.config.whaleTunnel_config import whale_tunnel_config
from app.common.logging_utils import title_handler, info_handler, debug_handler, warning_handler, error_handler
from app.common.utils import service_jvm_stack_size, service_jvm_young_generation_size
from app.cluster.cluster_for_ssh.tools.node_connect import node_connect
from app.cluster.config.cluster_node_config import scheduler_setup_config, get_services_by_ip, get_ip_by_service, \
    get_service_jvm_size
from app.cluster.cluster_for_ssh.tools.file_tool import FileTool
from app.cluster.cluster_for_ssh.tools.path_tool import PathTool
from app.cluster.cluster_for_ssh.tools.command_execution import execute_command

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class ConfigurationFileUpdate:
    def __init__(self, host, ssh_client, sftp_client, **kwargs):
        self.host = host
        self.ssh_client = ssh_client
        self.sftp_client = sftp_client
        self.result_dict = kwargs.get("result_dict", {})

        whale_scheduler_current_path = os.path.join(
            deployment_config.deployment_dir, "current", "whalestudio"
        )

        self.scheduler_service_config_path = {
            "api": os.path.join(
                whale_scheduler_current_path, "api-server", "conf"
            ),
            "master": os.path.join(
                whale_scheduler_current_path, "master-server", "conf"
            ),
            "worker": os.path.join(
                whale_scheduler_current_path, "worker-server", "conf"
            ),
            "alert": os.path.join(
                whale_scheduler_current_path, "alert-server", "conf"
            )
        }

        self.scheduler_config_path = os.path.join(
            whale_scheduler_current_path, "bin", "env", "whalescheduler_env.sh"
        )
        self.whale_tunnel_config_path = os.path.join(
            deployment_config.deployment_dir, "current", "whaletunnel", "config"
        )
        self.file_tool = FileTool(
            sftp_client = sftp_client,
            ssh_client = ssh_client
        )
        self.path_tool = PathTool(
            sftp_client = sftp_client
        )

    def run(self):
        scheduler_service_config_update_result = self.update_scheduler_service_config()
        if not scheduler_service_config_update_result[0]:
            return False, scheduler_service_config_update_result[1]
        if deployment_config.deploy_whaletunnel:
            whale_tunnel_config_update_result = self.update_whaleTunnel_config()
            if not whale_tunnel_config_update_result[0]:
                return False, whale_tunnel_config_update_result[1]
        return True, "Update configuration file successfully."

    def update_scheduler_service_config(self):
        """
        Update the scheduler service configuration files.
        :return: Tuple (success, message)
        """
        title_handler(message = f"Updating scheduler service configuration files on {self.host}".center(80, "*"))
        warning_handler(message = f"Updating scheduler service configuration files on {self.host}. Please wait...")
        for update_scheduler_config_task in [
            self._update_scheduler_startup_config,
            self._update_scheduler_jvm_config,
            self._sync_file_to_scheduler_config_dir,
        ]:
            start_time = time.time()
            success, message = update_scheduler_config_task()
            if not success:
                return False, message

            if update_scheduler_config_task == self._sync_file_to_scheduler_config_dir and message == "No other files to sync.":
                debug_handler(message = "No other files to sync.")
                continue

            end_time = time.time()
            time_consumption = end_time - start_time
            if time_consumption > 60:
                debug_handler(
                    message = f"{message} on {self.host} took {time_consumption / 60:.2f} minutes."
                )
                self.result_dict[self.host] += [
                    f"{message} took {time_consumption / 60:.2f} minutes."
                ]
            else:
                debug_handler(message = f"{message} on {self.host} took {time_consumption:.2f} seconds.")
                self.result_dict[self.host] += [
                    f"{message} took {time_consumption:.2f} seconds."
                ]

        return True, "Update scheduler service configuration files successfully."

    def _update_scheduler_startup_config(self):
        """
        Update the scheduler startup configuration file.
        :return:
        """
        write_status, write_message = self.file_tool.write_file(
            file_path = self.scheduler_config_path,
            content = scheduler_setup_config(ip = self.host),
            file_type = "text"
        )
        if not write_status:
            return False, write_message
        info_handler(
            message = f"Host: {self.host}. Scheduler startup configuration file has been updated successfully."
        )
        return True, "update scheduler startup configuration"

    def _update_scheduler_jvm_config(self):
        """
        Update the scheduler JVM configuration file.
        :return:
        """
        # 1. 获取当前节点所有的服务
        scheduler_service_list = [
            service for service in get_services_by_ip(ip = self.host) if service != "whaletunnel"
        ]
        for service in scheduler_service_list:
            service_jvm_size = get_service_jvm_size(ip = self.host, service_name = service)
            service_jvm_config_path = os.path.join(
                os.path.dirname(
                    self.scheduler_service_config_path.get(
                        service
                    )
                ), "bin", "jvm_args_env.sh"
            )
            if not self.file_tool.is_file_exist(file_path = service_jvm_config_path):
                error_handler(
                    message = f"Host: {self.host}. JVM configuration file for {service} does not exist: {service_jvm_config_path}. Will be skipped."
                )
                continue
            # 读取现有的 JVM 配置文件内容
            read_file_status, jvm_config_content = self.file_tool.read_file(
                file_path = service_jvm_config_path, file_type = "text"
            )
            if not read_file_status:
                error_handler(
                    message = f"Host: {self.host}. Failed to read JVM configuration file for {service}: {service_jvm_config_path}. Will be skipped."
                )
                continue
            jvm_config_content = [
                line.strip() for line in jvm_config_content if not line.startswith("-Xm")
            ]
            jvm_config_content += [
                f"-Xms{service_jvm_stack_size(jvm_stack_size = service_jvm_size)}",
                f"-Xmx{service_jvm_stack_size(jvm_stack_size = service_jvm_size)}",
                f"-Xmn{service_jvm_young_generation_size(jvm_stack_size = service_jvm_size)}"
            ]
            write_status, write_message = self.file_tool.write_file(
                file_path = service_jvm_config_path,
                content = jvm_config_content,
                file_type = "text"
            )
            if not write_status:
                error_handler(
                    message = f"Host: {self.host}. Failed to write JVM configuration file for {service}: {write_message}. Will be skipped."
                )
                continue
            debug_handler(
                message = f"Host: {self.host}. JVM configuration file for {service} has been updated successfully."
            )
            info_handler(
                message = f"Host: {self.host}. JVM configuration file for {service} has been updated successfully."
            )
        return True, "Update scheduler JVM configuration"

    def _sync_file_to_scheduler_config_dir(self):
        """
        Synchronize the configuration files to the scheduler configuration directory.
        :return:
        """
        other_files = other_files_to_sync()
        if not other_files:
            debug_handler(message = "No other files to sync.")
            return True, "No other files to sync."
        for file_path in other_files:
            file_name = os.path.basename(file_path)
            for service, config_path in self.scheduler_service_config_path.items():
                target_file_path = os.path.join(config_path, file_name)
                put_status, put_message = self.file_tool.push_file(
                    local_file_path = file_path,
                    remote_file_path = target_file_path,
                )
                if not put_status:
                    error_handler(
                        message = f"Host: {self.host}. Failed to sync file {file_name} to {target_file_path}: {put_message}."
                    )
                    continue
                debug_handler(
                    message = f"Host: {self.host}. File {file_name} has been synced to {target_file_path} successfully."
                )
            info_handler(
                message = f"Host: {self.host}. File {file_name} has been synced to scheduler configuration directory successfully."
            )
        return True, "Sync file to scheduler configuration directory"

    def update_whaleTunnel_config(self):
        """
        Update the whale tunnel configuration file.
        :return:
        """
        title_handler(message = f"Updating whale tunnel configuration file on {self.host}".center(80, "*"))
        warning_handler(message = f"Updating whale tunnel configuration file on {self.host}. Please wait...")

        if "whaletunnel" not in get_services_by_ip(ip = self.host):
            debug_handler(message = "Host: {self.host}. Whale tunnel service does not exist.")
            update_whaleTunnel_task_list = [
                self._update_hazelcast_client_config
            ]
        else:
            update_whaleTunnel_task_list = [
                self._update_whaleTunnel_config,
                self._update_whaleTunnel_jvm_config,
                self._update_hazelcast_config,
                self._update_hazelcast_client_config,
            ]

        for update_whaleTunnel_config_task in update_whaleTunnel_task_list:
            start_time = time.time()
            success, message = update_whaleTunnel_config_task()
            if not success:
                return False, message

            end_time = time.time()
            time_consumption = end_time - start_time
            if time_consumption > 60:
                debug_handler(
                    message = f"{message} on {self.host} took {time_consumption / 60:.2f} minutes."
                )
                self.result_dict[self.host] += [
                    f"{message} took {time_consumption / 60:.2f} minutes."
                ]
            else:
                debug_handler(message = f"{message} on {self.host} took {time_consumption:.2f} seconds.")
                self.result_dict[self.host] += [
                    f"{message} took {time_consumption:.2f} seconds."
                ]
        return True, "Update whale tunnel configuration file"

    def _update_whaleTunnel_config(self):
        """
        Update the whale tunnel configuration file.
        :return:
        """
        write_status, write_message = self.file_tool.write_file(
            file_path = os.path.join(
                self.whale_tunnel_config_path, "seatunnel.yaml"
            ),
            content = whale_tunnel_config(),
            file_type = "yaml"
        )
        if not write_status:
            return False, write_message
        info_handler(message = f"Host: {self.host}. Whale tunnel configuration file has been updated successfully.")
        return True, "Update whale tunnel configuration file"

    def _update_whaleTunnel_jvm_config(self):
        """
        Update the whale tunnel JVM configuration file.
        :return:
        """
        whale_tunnel_jvm_file_path = os.path.join(
            self.whale_tunnel_config_path, "jvm_options"
        )
        read_file_status, jvm_config_content = self.file_tool.read_file(
            file_path = whale_tunnel_jvm_file_path,
            file_type = "text"
        )
        if not read_file_status:
            return False, f"Failed to read whale tunnel JVM configuration file: {jvm_config_content}"
        current_jvm_config = [
            line.strip() for line in jvm_config_content if
            line.strip() and not line.strip().startswith(("-Xm", "-XX:ConcGCThreads"))
        ]
        # whale tunnel JVM 大小
        whale_tunnel_jvm_size = get_service_jvm_size(ip = self.host, service_name = "whaletunnel")
        current_jvm_config += [
            f"-Xms{service_jvm_stack_size(jvm_stack_size = whale_tunnel_jvm_size)}",
            f"-Xmx{service_jvm_stack_size(jvm_stack_size = whale_tunnel_jvm_size)}",
        ]

        # 获取远端主机的CPU核数
        cpu_cores_result = execute_command(
            ssh_client = self.ssh_client,
            command = "nproc --all"
        )
        if cpu_cores_result[0] != 0:
            debug_handler(message = f"Failed to get CPU cores on {self.host}: {cpu_cores_result[1]}")
            cpu_cores = 8
        else:
            cpu_cores = int(cpu_cores_result[1].strip())

        if cpu_cores > 8:
            current_jvm_config.append(
                "-XX:ConcGCThreads=6"
            )
        else:
            current_jvm_config.append(
                f"-XX:ConcGCThreads={max(1, int(cpu_cores * 5 / 8))}"
            )
        debug_handler(message = f"Host: {self.host}. Whale tunnel JVM configuration: {current_jvm_config}")
        write_status, write_message = self.file_tool.write_file(
            file_path = whale_tunnel_jvm_file_path,
            content = current_jvm_config,
            file_type = "text"
        )
        if not write_status:
            return False, f"Failed to write whale tunnel JVM configuration file: {write_message}"
        info_handler(message = f"Host: {self.host}. Whale tunnel JVM configuration file has been updated successfully.")
        return True, "Update whale tunnel JVM configuration file"

    def _update_hazelcast_config(self):
        """
        Update the hazelcast configuration file.
        :return:
        """
        hazelcast_config_path = os.path.join(self.whale_tunnel_config_path, "hazelcast.yaml")
        if not self.file_tool.is_file_exist(file_path = hazelcast_config_path):
            error_handler(
                message = f"Host: {self.host}. Hazelcast configuration file does not exist: {hazelcast_config_path}. Will be skipped."
            )
            return True, "Hazelcast configuration file does not exist"
        read_file_status, hazelcast_config_content = self.file_tool.read_file(
            file_path = hazelcast_config_path,
            file_type = "yaml"
        )
        if not read_file_status:
            error_handler(
                message = f"Host: {self.host}. Failed to read hazelcast configuration file: {hazelcast_config_content}. Will be skipped."
            )
            return False, f"Failed to read hazelcast configuration file: {hazelcast_config_content}"
        new_hazelcast_config = hazelcast_config_content.copy()
        new_hazelcast_config[
            "hazelcast"
        ][
            "network"
        ][
            "join"
        ][
            "tcp-ip"
        ][
            "member-list"
        ] = get_ip_by_service(
            service_name = "whaletunnel",
        )
        debug_handler(message = f"Host: {self.host}. New hazelcast configuration: {new_hazelcast_config}")
        write_status, write_message = self.file_tool.write_file(
            file_path = hazelcast_config_path,
            content = new_hazelcast_config,
            file_type = "yaml"
        )
        if not write_status:
            error_handler(
                message = f"Host: {self.host}. Failed to write hazelcast configuration file: {write_message}. Will be skipped."
            )
            return False, f"Failed to write hazelcast configuration file: {write_message}"
        info_handler(message = f"Host: {self.host}. Hazelcast configuration file has been updated successfully.")
        return True, "Update hazelcast configuration file"

    def _update_hazelcast_client_config(self):
        """
        Update the hazelcast client configuration file.
        :return:
        """
        hazelcast_client_config_path = os.path.join(self.whale_tunnel_config_path, "hazelcast-client.yaml")
        if not self.file_tool.is_file_exist(file_path = hazelcast_client_config_path):
            error_handler(
                message = f"Host: {self.host}. Hazelcast client configuration file does not exist: {hazelcast_client_config_path}. Will be skipped."
            )
            return True, "Hazelcast client configuration file does not exist"
        read_file_status, hazelcast_client_config_content = self.file_tool.read_file(
            file_path = hazelcast_client_config_path,
            file_type = "yaml"
        )
        if not read_file_status:
            error_handler(
                message = f"Host: {self.host}. Failed to read hazelcast client configuration file: {hazelcast_client_config_content}. Will be skipped."
            )
            return False, f"Failed to read hazelcast client configuration file: {hazelcast_client_config_content}"
        new_hazelcast_client_config = hazelcast_client_config_content.copy()
        new_hazelcast_client_config[
            "hazelcast-client"
        ][
            "network"
        ][
            "cluster-members"
        ] = [
            f"{ip}:5801" for ip in get_ip_by_service(
                service_name = "whaletunnel",
            )
        ]
        debug_handler(message = f"Host: {self.host}. New hazelcast client configuration: {new_hazelcast_client_config}")
        write_status, write_message = self.file_tool.write_file(
            file_path = hazelcast_client_config_path,
            content = new_hazelcast_client_config,
            file_type = "yaml"
        )
        if not write_status:
            error_handler(
                message = f"Host: {self.host}. Failed to write hazelcast client configuration file: {write_message}. Will be skipped."
            )
            return False, f"Failed to write hazelcast client configuration file: {write_message}"
        info_handler(message = f"Host: {self.host}. Hazelcast client configuration file has been updated successfully.")

        return True, "Update hazelcast client configuration file"


def update_configuration_file(host, *args, **kwargs):
    """
    Update the configuration file for the Whale Studio service.

    :param host: The host where the service is running.
    :param args: Additional arguments.
    :param kwargs: Additional keyword arguments.
    :return: The result of the operation.
    """
    result_dict = kwargs.get("result_dict", {})
    ssh_connect_start_time = time.time()
    ssh_client, sftp_client, connect_result = node_connect(host)
    if not ssh_client or not sftp_client:
        return False, connect_result
    result_dict[host] = []
    ssh_connect_end_time = time.time()
    ssh_connect_time_consumption = ssh_connect_end_time - ssh_connect_start_time
    if ssh_connect_time_consumption > 60:
        result_dict[host] += [
            f"SSH connection time consuming {ssh_connect_time_consumption / 60:.2f} minutes."
        ]
    else:
        result_dict[host] += [
            f"SSH connection time consuming {ssh_connect_time_consumption:.2f} seconds."
        ]
    try:

        return ConfigurationFileUpdate(
            host = host,
            ssh_client = ssh_client,
            sftp_client = sftp_client,
            **kwargs
        ).run()

    except Exception as e:
        debug_handler(message = f"Failed to install Whale Studio on {host}. Error: {e}")
        debug_handler(message = traceback.format_exc())
        return False, f"Failed to install Whale Studio. Error: {e}"
