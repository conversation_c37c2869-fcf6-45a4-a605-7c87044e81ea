#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : whaleStudio_db_operation.py
# @Time    : 2025/06/07 14:17
# <AUTHOR> chenyi<PERSON>i
# @Version : 1.0

import os
import sys
import traceback

from app.common.logging_utils import error_handler, info_handler, warning_handler, debug_handler

from app.config.deployment_config import deployment_config, load_env_command
from app.config.scheduler_config import metabase_logs_save_path, metabase_initialization_config
from app.cluster.cluster_for_ssh.tools.file_tool import FileTool

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class DBOperation:
    def __init__(self, operation_type, ssh_client, sftp_client, show_log = False):
        """
        初始化数据库操作类
        :param operation_type:
        """
        self.operation_type = operation_type
        self.metabase_init_script = os.path.join(
            deployment_config.deployment_dir,
            "current",
            "whalestudio",
            "tools",
            "bin",
            "upgrade-schema.sh"
        )
        self.show_log = show_log
        self.metabase_logs_save_path = os.path.join(
            os.getenv(
                "LOGS_PATH", metabase_logs_save_path()
            ), f"{operation_type}.log"
        )

        self.ssh_client = ssh_client
        self.sftp_client = sftp_client

        self.file_tool = FileTool(
            ssh_client = ssh_client,
            sftp_client = sftp_client
        )

    def run(self):
        """
        执行数据库操作
        :return:
        """
        # 1. 写入 metabase 配置文件
        self._update_metabase_config()

        match self.operation_type:
            case "db_init":
                self.metabase_init(display_name = "Initialize Metabase")
            case "db_upgrade":
                self.metabase_init(display_name = "Upgrade Metabase")
            case _:
                error_handler(message = "Database operation type error!")
                sys.exit(1)

    def metabase_init(self, display_name):
        """
        初始化 metabase
        :param display_name:
        :return:
        """
        stdin, stdout, stderr = self.ssh_client.exec_command(
            f"{load_env_command()} && /bin/bash {self.metabase_init_script}"
        )
        if not self.show_log or self.show_log == "None":
            warning_handler(message = f"{display_name} is running, please wait for a moment...")
        try:
            with open(self.metabase_logs_save_path, "w") as f:
                while not stdout.channel.exit_status_ready():
                    for line in iter(stdout.readline, ""):
                        sys.stdout.flush()
                        stdout_line = line.rstrip()
                        f.write(stdout_line + "\n")
                        if self.show_log and self.show_log != "None":
                            print(stdout_line)
        except Exception as e:
            error_handler(message = f"{display_name} failed! Error message: {e}")
            debug_handler(message = traceback.format_exc())
            sys.exit(1)
        debug_handler(message = f"Stdout: {stdout.read().strip()}")
        debug_handler(message = f"Stderr: {stderr.read().strip()}")
        debug_handler(message = f"Exit status: {stdout.channel.recv_exit_status()}")
        if stdout.channel.recv_exit_status() == 0:
            info_handler(message = f"{display_name} succeeded!")
            sys.stdout.flush()
            warning_handler(message = f"Log file path: {self.metabase_logs_save_path}")
            sys.exit(0)
        else:
            error_handler(message = f"{display_name} failed!")
            error_handler(message = "Please check the log file for more details.")
            error_handler(message = f"Log file path: {self.metabase_logs_save_path}")
            sys.exit(1)



    def _update_metabase_config(self):
        """
        更新 metabase 配置文件
        :return:
        """
        metabase_config_path = os.path.join(
            deployment_config.deployment_dir,
            "current",
            "whalestudio",
            "tools",
            "conf",
            "whalescheduler_env.sh"
        )
        write_status, write_message = self.file_tool.write_file(
            file_path = metabase_config_path,
            content = metabase_initialization_config(),
            file_type = "text"
        )
        if not write_status:
            error_handler(message = f"Failed to update metabase configuration file! Error message: {write_message}")
            sys.exit(1)
    #
    # def _print_log(self):
    #     """
    #     打印日志
    #     :return:
    #     """
    #     # 循环20秒，等待日志文件生成
    #     for i in range(20):
    #         if not FileTool.is_file_exists(file_path = self.metabase_logs_save_path):
    #             time.sleep(1)
    #             continue
    #         break
    #     tail_logs(
    #         log_file = self.metabase_logs_save_path,
    #         lines = 20
    #     )#
