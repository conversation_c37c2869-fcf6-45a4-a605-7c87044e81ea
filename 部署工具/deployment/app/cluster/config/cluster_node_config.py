#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : cluster_node_config.py
# @Time    : 2025/06/06 15:44
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys
import traceback

from app.config.deployment_config import deployment_config
from app.config.scheduler_config import SchedulerConfig
from app.common.logging_utils import debug_handler

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))

"""
Config file for cluster node. | 集群节点配置文件
"""


# 根据IP获取节点下的所有服务名称
def get_services_by_ip(ip: str) -> list:
    """
    Get all service names under the specified IP. | 获取指定IP下的所有服务名称
    """
    # Placeholder implementation, replace with actual logic to retrieve services.
    default_services = ["api", "master", "worker", "alert"]
    if deployment_config.deploy_whaletunnel:
        default_services.append("whaletunnel")
    node_services = deployment_config.cluster_nodes.get(ip, {}).get("roles", "all")
    debug_handler(message = f"Node services: {node_services}, Host IP: {ip}")
    if node_services == "all":
        return default_services
    else:
        if isinstance(node_services, str):
            node_services = [
                node_service.strip() for node_service in node_services.split(",") if
                node_service.strip() in default_services
            ]
            return node_services
        if isinstance(node_services, list):
            return [service.strip() for service in node_services if service.strip() in default_services]


# 根据服务名称获取节点IP列表
def get_ip_by_service(service_name: str) -> list:
    """
    Get the IP of the specified service. | 获取指定服务的IP
    :param service_name:
    :return:
    """
    # Placeholder implementation, replace with actual logic to retrieve IP.

    node_list = []

    for ip, _ in deployment_config.cluster_nodes.items():
        if service_name in get_services_by_ip(ip):
            node_list.append(ip)
    return node_list


# 获取节点的高优先级配置
# 作用
def get_high_priority_config(ip: str, effect = "diff") -> list:
    """
    Get the high priority configuration for the specified node IP. | 获取指定节点IP的高优先级配置
    :param ip: Node IP address.
    :return: High priority configuration dictionary.
    """
    high_priority_config = []
    node_config = deployment_config.cluster_nodes.get(ip, {})

    network_interface = node_config.get("network_interface", None)
    worker_pool_size = node_config.get("worker_pool_size", None)
    try:
        if effect == "diff":
            if network_interface:
                high_priority_config.append(
                    "DOLPHIN_SCHEDULER_NETWORK_INTERFACE_PREFERRED"
                )
            if worker_pool_size:
                high_priority_config.append(
                    "WORKER_SYNC_TASK_THREAD_POOL_SIZE"
                )
            return high_priority_config
        else:
            if network_interface:
                high_priority_config.append(
                    f'export DOLPHIN_SCHEDULER_NETWORK_INTERFACE_PREFERRED="{network_interface}"'
                )
            if worker_pool_size and "worker" in get_services_by_ip(ip):
                high_priority_config.append(
                    f"export WORKER_SYNC_TASK_THREAD_POOL_SIZE={int(worker_pool_size)}"
                )
            return high_priority_config
    except Exception as e:
        debug_handler(message = f"Error in get_high_priority_config: {e}")
        debug_handler(message = traceback.format_exc())
        return []


def scheduler_setup_config(ip: str):
    """
    Get the scheduler setup configuration for the specified node IP. | 获取指定节点IP的调度器安装配置
    :param ip:
    :return:
    """
    scheduler_config = SchedulerConfig()
    node_config = deployment_config.cluster_nodes.get(ip, {})

    scheduler_startup_config_content = (scheduler_config.metabase_config() +
                                        scheduler_config.register_config() +
                                        scheduler_config.logging_config(
                                            node_config.get(
                                                "service_log_dir", None
                                            )
                                        ) + scheduler_config.reset_user_password_config() +
                                        scheduler_config.whaleTunnel_config() +
                                        scheduler_config.other_config() +
                                        scheduler_config.environment_config(
                                            get_high_priority_config(ip = ip, effect = "diff")
                                        ) + get_high_priority_config(ip = ip, effect = "config"))
    return scheduler_startup_config_content


def get_service_jvm_size(ip: str, service_name: str):
    """
    Get the JVM size configuration for the specified service on the given node IP. | 获取指定节点IP上指定服务的JVM大小配置
    :param ip:
    :param service_name:
    :return:
    """
    node_jvm_size = deployment_config.cluster_nodes.get(ip, {}).get(
        "roles_memory_size", {}
    ).get(
        service_name, deployment_config.roles_memory_size.get(
            service_name, 2
        )
    )
    return node_jvm_size
