#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : whaleTunnel_config.py
# @Time    : 2025/06/04 16:18
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys
import traceback

import yaml

# from app.common.utils import get_parent_directory
from app.common.logging_utils import error_handler, debug_handler
from app.config.deployment_config import deployment_config

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


def whale_tunnel_config(whale_tunnel_connection = None):
    """
    Get the configuration of whale tunnel. | 获取whale tunnel的配置
    :return:
    """
    if not whale_tunnel_connection:
        # whale_tunnel_path = os.path.join(
        #     ".", "config", "seatunnel.yaml"
        # )
        whale_tunnel_path = os.getenv(
            "WHALETUNNEL_CONFIG_PATH", os.path.join(".", "config", "seatunnel.yaml")
        )
        # print(whale_tunnel_path)
        # print(os.getcwd())
        if not os.path.isfile(whale_tunnel_path):
            error_handler(message = f"Whale tunnel configuration file {whale_tunnel_path} does not exist.")
            debug_handler(message = traceback.format_exc())
            sys.exit(1)

        try:
            with open(whale_tunnel_path, "r") as f:
                whale_tunnel_connection = yaml.safe_load(f)
        except PermissionError:
            error_handler(
                message = f"Failed to load whale tunnel configuration file {whale_tunnel_path}. Permission denied."
            )
            sys.exit(1)
        except Exception as e:
            error_handler(message = f"Failed to load whale tunnel configuration file {whale_tunnel_path}. {e}")
            sys.exit(1)

    new_whale_tunnel_config = whale_tunnel_connection.copy()

    event_report_http_url = whale_tunnel_connection.get(
        "seatunnel", {}
    ).get(
        "engine", {}
    ).get(
        "event-report-http", {}
    ).get(
        "url", None
    )
    if not event_report_http_url or event_report_http_url in [
        "http://localhost:12345/dolphinscheduler/ws/seaTunnel/openapi/event",
        "http://127.0.0.1:12345/dolphinscheduler/ws/seaTunnel/openapi/event"]:
        new_whale_tunnel_config["seatunnel"]["engine"]["event-report-http"][
            "url"] = f"{deployment_config.api_workflow_address.strip('/')}/dolphinscheduler/ws/seaTunnel/openapi/event"

    license_service_http = whale_tunnel_connection.get(
        "seatunnel", {}
    ).get(
        "engine", {}
    ).get(
        "license-service-http", {}
    ).get(
        "url", None
    )
    if not license_service_http or license_service_http in [
        "http://localhost:12345/dolphinscheduler/ws/seaTunnel/openapi/get-valid-license",
        "http://127.0.0.1:12345/dolphinscheduler/ws/seaTunnel/openapi/get-valid-license"]:
        new_whale_tunnel_config["seatunnel"]["engine"]["license-service-http"][
            "url"] = f"{deployment_config.api_workflow_address.strip('/')}/dolphinscheduler/ws/seaTunnel/openapi/get-valid-license"
    return new_whale_tunnel_config
