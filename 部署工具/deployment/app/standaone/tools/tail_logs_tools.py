#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : tail_logs_tools.py
# @Time    : 2025/06/04 18:57
# <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import sys
import subprocess

from app.common.logging_utils import error_handler

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


def tail_logs(log_file, lines = 10):

    """Tail the log file and return the last n lines. | 读取日志文件并返回最后n行"""
    try:
        tail_logs_result = subprocess.Popen(
            [
                "tail",
                "-200f",
                log_file
            ],
            stdout = subprocess.PIPE,
            stderr = subprocess.PIPE,
            text = True
        )
        while True:
            line = tail_logs_result.stdout.readline().rstrip()
            if not line:
                continue
            print(line)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        error_handler(message = f"Error tailing log file {log_file}: {e}")
        sys.exit(1)
