#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : command_execution_tools.py
# @Time    : 2025/06/04 18:03
# <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import subprocess
import sys

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


def execute_command(command):
    """Execute a command in the current directory. | 在当前目录下执行命令"""
    try:
        command_execution_result = subprocess.run(
            command,
            shell = True,
            check = True,
            stdout = subprocess.PIPE,
            stderr = subprocess.PIPE,
            timeout = 60
        )
        # 执行状态
        command_exit_status = command_execution_result.returncode
        # 执行结果
        command_output = command_execution_result.stdout.decode('utf-8')
        # 错误信息
        command_error = command_execution_result.stderr.decode('utf-8')
        return command_exit_status, command_output, command_error
    except subprocess.CalledProcessError as e:
        # 执行状态
        command_exit_status = e.returncode
        # 执行结果
        command_output = e.stdout.decode('utf-8')
        # 错误信息
        command_error = e.stderr.decode('utf-8')
        return command_exit_status, command_output, command_error
    except Exception as e:
        return 1, '', str(e)
