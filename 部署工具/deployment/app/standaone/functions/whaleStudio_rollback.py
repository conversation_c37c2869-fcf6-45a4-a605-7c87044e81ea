#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : whaleStudio_rollback.py
# @Time    : 2025/06/16 15:37
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys

from app.common.logging_utils import warning_handler, error_handler, info_handler
from app.config.deployment_config import deployment_config
from app.standaone.tools.file_tool import FileTool
from app.standaone.tools.path_tool import PathTool

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


def rollback():
    """Rollback the Whale Studio to the previous version. | 回滚Whale Studio到上一个版本"""
    # 1. 检查是否存在备份目录
    current_soft_links = os.path.join(
        CURRENT_DIRECTORY, "current_symlink.json"
    )
    if not FileTool.is_file_exists(file_path = current_soft_links):
        error_handler(message = "Cannot roll back as the previous deployment record cannot be found!")
        sys.exit(1)

    # 2. 读取备份目录
    read_status, backup_symlinks = FileTool.read_file(
        file_path = current_soft_links,
        file_type = "json"
    )
    if not read_status:
        error_handler(message = "Cannot read the previous deployment record!")
        sys.exit(1)
    # 3. 让用户确认是否回滚
    # 读取一下要回退到的历史版本
    whaleScheduler_rollback_path = [target_path for target_path in backup_symlinks.values() if
                                    target_path.endswith("whalescheduler")]
    if not whaleScheduler_rollback_path:
        error_handler(message = "Cannot find the previous version of Whale Scheduler!")
        sys.exit(1)
    whaleScheduler_rollback_path = whaleScheduler_rollback_path[0]
    rollback_version_file = os.path.join(
        whaleScheduler_rollback_path, "version.properties"
    )
    # 读取version.properties文件，获取Whale Studio版本号
    if not FileTool.is_file_exists(file_path = rollback_version_file):
        error_handler(message = "Cannot find the version.properties file in the previous version of Whale Scheduler!")
        sys.exit(1)
    read_status, version_properties = FileTool.read_file(
        file_path = rollback_version_file,
        file_type = "text"
    )
    if not read_status:
        error_handler(message = "Cannot read the version.properties file in the previous version of Whale Scheduler!")
        sys.exit(1)
    for line in version_properties:
        if line.startswith("git.build.version="):
            rollback_version = line.split("=")[1].strip()
            break
    else:
        error_handler(
            message = "Cannot find the version number in the version.properties file in the previous version of Whale Scheduler!"
        )
        sys.exit(1)

    # 获取一下当前的Whale Studio版本号
    current_version_file = os.path.join(
        deployment_config.deployment_dir, "current", "whalestudio", "version.properties"
    )
    if not FileTool.is_file_exists(file_path = current_version_file):
        pass
    else:
        # 读取version.properties文件，获取Whale Studio版本号
        for line in version_properties:
            if line.startswith("git.build.version="):
                current_version = line.split("=")[1].strip()
                break
        else:
            error_handler(
                message = "Cannot find the version number in the version.properties file in the current version of Whale Studio!"
            )
            sys.exit(1)
        if current_version == rollback_version:
            warning_handler(
                message = "The current version of Whale Studio is the same as the version to be rolled back to!"
            )
            warning_handler(message = "No rollback is needed.")
            if FileTool.is_file_exists(
                    file_path = current_soft_links
            ):
                FileTool.delete_file(file_path = current_soft_links)
            sys.exit(0)

    # 提醒用户确认有数据库的备份
    warning_handler(message = "Please make sure you have a backup of the database before rolling back Whale Studio.")
    for index in range(3):
        # 给用户三次机会确认回滚
        confirm_message = f"Are you sure you want to roll back Whale Studio to the previous version ({rollback_version})? (This action cannot be undone) (Y/N): "
        confirm_status = input(confirm_message)
        if confirm_status.lower() in ["y", "yes"]:
            break
        elif confirm_status.lower() in ["n", "no"]:
            sys.exit(0)
        else:
            warning_handler(message = "Invalid input! Please enter Y or N.")
    # 4. 回滚Whale Studio
    for symlink_path, target_path in backup_symlinks.items():
        # 4.1 删除 symlink_path
        delete_status, delete_message = PathTool.delete_symlink(
            symlink_path = symlink_path
        )
        if not delete_status:
            error_handler(message = delete_message)
            sys.exit(1)
        # 4.2 重建 symlink_path
        create_status, create_message = PathTool.create_symlink(
            symlink_path = symlink_path,
            target_path = target_path
        )
        if not create_status:
            error_handler(message = create_message)
            sys.exit(1)
    # 5. 删除备份目录
    delete_status, delete_message = FileTool.delete_file(
        file_path = current_soft_links
    )
    if not delete_status:
        error_handler(message = delete_message)
        sys.exit(1)
    # 6. 完成回滚
    info_handler(message = "Whale Studio has been rolled back to the previous version.")
    # 7. 提示用户需要回滚数据库并重启Whale Scheduler
    warning_handler(message = "Please roll back the database and restart Whale Scheduler to complete the rollback.")
