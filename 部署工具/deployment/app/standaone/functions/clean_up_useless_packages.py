#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : clean_up_useless_packages.py
# @Time    : 2025/06/05 11:27
# <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import sys
import traceback

from app.common.utils import task_running_time
from app.common.logging_utils import info_handler, error_handler, warning_handler, debug_handler, title_handler, console
from app.config.deployment_config import deployment_config
from app.standaone.tools.path_tool import PathTool
from app.standaone.tools.file_tool import FileTool

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class CleanUpUselessPackages:
    """
    This class is used to clean up useless packages.
    """

    def __init__(self):
        pass

    @task_running_time(task_name = "Clean Up Useless Packages")
    def run(self):
        """
        This method is used to clean up useless packages.
        """
        title_handler("Clean Up Useless Packages".center(100, "="))
        with console.status("[bold green]Cleaning up useless packages...[/bold green]"):
            for clean_up_task in [
                self.clean_up_compressed_packages,  # 清理无用的软件包
                # 清理无用的解压文件
                self.clean_up_extracted
            ]:
                clean_up_task()

    def clean_up_compressed_packages(self):
        """
        This method is used to clean up useless packages.
        """
        title_handler("Clean Up Useless Compressed package".center(60, "*"))
        if not PathTool.is_directory_exist(deployment_config.package_dir):
            warning_handler(message = "Package directory does not exist, skipping cleanup.")
            return

            # 所有的压缩包
        compressed_packages = [
            os.path.join(deployment_config.package_dir, file_name)
            for file_name in os.listdir(deployment_config.package_dir)
            if file_name.endswith(".tar.gz")
        ]
        if not compressed_packages:
            warning_handler(message = "No compressed packages found, skipping cleanup.")
            return

        # 去除最新的压缩包
        latest_package = max(compressed_packages, key = os.path.getctime)
        compressed_packages.remove(latest_package)
        if not compressed_packages:
            warning_handler(message = "No compressed packages found, skipping cleanup.")
            return
        # 删除其他的压缩包
        for package in compressed_packages:
            try:
                delete_status, delete_message = FileTool.delete_file(package)
                if delete_status:
                    info_handler(message = f"Deleted compressed package: {os.path.basename(package)} successfully.")
                else:
                    error_handler(
                        message = f"Failed to delete compressed package: {os.path.basename(package)}, {delete_message}."
                    )
            except Exception as e:
                error_handler(message = f"Failed to delete compressed package: {os.path.basename(package)}, {e}.")
                debug_handler(message = traceback.format_exc())

    def clean_up_extracted(self):
        """
        This method is used to clean up useless packages.
        """
        title_handler("Clean Up Useless Extracted package".center(60, "*"))
        # 1. 获取所有解压后的目录
        # 解压后目录
        extracted_path = os.path.join(deployment_config.deployment_dir, "current_package")
        # 软连接目录
        symlink_path = os.path.join(deployment_config.deployment_dir, "current")
        extracted_dirs = [
            os.path.join(extracted_path, dir_name)
            for dir_name in os.listdir(extracted_path)
        ]
        debug_handler(message = f"Extracted directories: {extracted_dirs}.")
        # 2. 获取当前软连接所对应的目录
        # 所有软连接
        symlink_dirs = [
            os.path.join(symlink_path, dir_name)
            for dir_name in os.listdir(symlink_path) if os.path.islink(os.path.join(symlink_path, dir_name))
        ]
        # 3. 找到当前软连接所对应的目录
        corresponding_directory_for_soft_links = list(
            set(
                [
                    os.path.dirname(symlink_dir)
                    for symlink_dir in list(
                    PathTool.get_symlink_target(
                        sub_paths = symlink_dirs
                    ).values()
                )
                ]
            )
        )
        debug_handler(message = f"Current symlink directories: {corresponding_directory_for_soft_links}.")
        # 4. 获取是否有上次记录的目录
        record_files = os.path.join(
            CURRENT_DIRECTORY, "current_symlink.json"
        )
        if FileTool.is_file_exists(file_path = record_files):
            debug_handler(
                message = f"Record file exists, reading the last recorded directory: {record_files}."
            )
            read_status, read_message = FileTool.read_file(
                file_path = record_files,
                file_type = "json"
            )
            # 记录软连接对应的目录
            if read_status:
                record_corresponding_directory_for_soft_links = list(
                    set(
                        [
                            os.path.dirname(record_dir)
                            for record_dir in list(read_message.values())
                        ]
                    )
                )
            else:
                debug_handler(message = f"Failed to read record file: {read_message}.")
                record_corresponding_directory_for_soft_links = []
        else:
            debug_handler(message = f"Record file does not exist, creating a new one: {record_files}.")
            record_corresponding_directory_for_soft_links = []
        debug_handler(message = f"Record symlink directories: {record_corresponding_directory_for_soft_links}.")
        # 5. 找到需要删除的目录
        delete_directories = [
            delete_dir for delete_dir in extracted_dirs
            if
            delete_dir not in corresponding_directory_for_soft_links and delete_dir not in record_corresponding_directory_for_soft_links
        ]
        if not delete_directories:
            warning_handler(message = "No useless directories found, skipping cleanup.")
            return
            # 6. 删除目录
        for delete_dir in delete_directories:
            try:
                delete_status, delete_message = PathTool.delete_directory(delete_dir)
                if delete_status:
                    info_handler(message = f"Deleted directory: {os.path.basename(delete_dir)} successfully.")
                else:
                    error_handler(
                        message = f"Failed to delete directory: {os.path.basename(delete_dir)}, {delete_message}."
                    )
            except Exception as e:
                error_handler(message = f"Failed to delete directory: {os.path.basename(delete_dir)}, {e}.")
                debug_handler(message = traceback.format_exc())
