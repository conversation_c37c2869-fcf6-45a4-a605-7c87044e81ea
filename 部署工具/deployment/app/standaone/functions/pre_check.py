#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : pre_check_for_standalone.py
# @Time    : 2025/06/03 17:39
# <AUTHOR> chenyi<PERSON>i
# @Version : 1.0

import os
import shutil
import subprocess
import sys
import traceback
import psutil
from app.common.logging_utils import info_handler, error_handler, debug_handler, console, title_handler
from app.common.utils import task_running_time
from app.config.deployment_config import load_env_command, deployment_config

from app.standaone.tools.path_tool import PathTool
from app.standaone.tools.file_tool import FileTool

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class PreDeploymentCheck:
    """
    Class to perform pre-deployment checks for a standalone application. | 独立应用程序的预部署检查类
    """

    def __init__(self):
        pass

    @task_running_time(task_name = "Pre-deployment checks for standalone application")
    def run(self):
        """
        Run all pre-deployment checks. | 运行所有预部署检查
        """
        title_handler(
            message = "Pre-deployment checks for standalone application".center(100, "=")
        )
        with console.status(
                "[bold green]Running pre-deployment checks for standalone application...[/bold green]"
        ):
            try:
                for pre_check_task in [
                    self.check_java_version,
                    # 检查磁盘写入权限
                    self.check_disk_write_permission,
                    # 检查磁盘剩余空间
                    self.check_disk_space,
                    self.check_memory,
                ]:
                    pre_check_task()
                info_handler(message = "All pre-deployment checks passed successfully.")
            except Exception as e:
                error_handler(f"An error occurred during pre-deployment checks: {e}")
                debug_handler(traceback.format_exc())
                sys.exit(1)

    def _parse_java_version(self, version_info):
        """
        提取 Java 版本号
        """
        version_line = version_info.splitlines()[0]
        java_version = version_line.split()[2].strip('"')
        return java_version.split(".")

    def check_java_version(self):
        """
        Check the Java version. | 检查Java版本
        """
        # 检查Java版本是否 >= 1.8.0_151 <= 0_371
        # 1. 获取Java版本号
        result = subprocess.run(
            f"{load_env_command()} && java -version", shell = True, capture_output = True, text = True
        )
        if result.returncode != 0 or not result.stderr:
            error_handler("Java is not installed or not found in the PATH.")
            sys.exit(1)
        # 2. 解析Java版本号
        version_info = result.stderr
        java_version = self._parse_java_version(version_info)
        if "openjdk" in version_info.lower():
            if int(java_version[0]) == 1 and int(java_version[1]) == 8:
                info_handler(message = f"OpenJDK version: {version_info.splitlines()[0]}")
            else:
                error_handler(f"OpenJDK version should be 1.8.x, but found {version_info.splitlines()[0]}")
                sys.exit(1)
        else:
            if (int(java_version[0]) == 1 and int(java_version[1]) == 8
                    and java_version[2] >= "0_151" and java_version[2] <= "0_371"):
                info_handler(message = f"Oracle Java version: {version_info.splitlines()[0]}")
            else:
                error_handler(
                    f"Oracle Java version should be 1.8.0_151-0_371, but found {version_info.splitlines()[0]}"
                )
                sys.exit(1)

    def check_disk_write_permission(self):
        """
        Check if the current user has write permission to the current directory. | 检查当前用户是否有写入权限到当前目录
        """
        # 1. 判断部署目录是否存在
        if not PathTool.is_directory_exist(path = deployment_config.deployment_dir):
            debug_handler(message = "Deployment directory does not exist.")
            # 创建部署目录
            create_status, create_message = PathTool.create_directory(
                path = deployment_config.deployment_dir
            )
            if not create_status:
                error_handler(message = f"Failed to create deployment directory: {create_message}")
                sys.exit(1)
        # 2. 检查当前用户是否有写入权限到部署目录
        write_file_status, write_file_message = FileTool.write_file(
            file_path = os.path.join(deployment_config.deployment_dir, "write_permission_test.txt"),
            content = "test"
        )
        if not write_file_status:
            error_handler(message = f"Failed to write file to deployment directory: {write_file_message}")
            sys.exit(1)
        # 3. 删除测试文件
        delete_file_status, delete_file_message = FileTool.delete_file(
            file_path = os.path.join(deployment_config.deployment_dir, "write_permission_test.txt")
        )
        if not delete_file_status:
            error_handler(message = f"Failed to delete test file from deployment directory: {delete_file_message}")
            sys.exit(1)
        info_handler(message = "Current user has write permission to the deployment directory.")

    def check_disk_space(self):
        """
        Check the remaining disk space. | 检查剩余磁盘空间
        """
        # 1. 获取部署目录的磁盘使用情况
        disk_usage = shutil.disk_usage(deployment_config.deployment_dir)
        disk_total = disk_usage.total
        disk_used = disk_usage.used
        disk_free = disk_usage.free
        disk_free_gb = disk_free / (1024 * 1024 * 1024)
        if disk_free_gb < 30:
            error_handler(
                message = f"There is not enough disk space for WhaleStudio Deployment. "
            )
            error_handler(
                message = "Deployment requires at least 30 GB of free disk space."
            )
            error_handler(message = f"Current free disk space: {disk_free_gb:.2f} GB.")
            sys.exit(1)
        debug_handler(message = f"Total deployment directory space: {disk_total / (1024 * 1024 * 1024):.2f} GB")
        debug_handler(message = f"Deployment directory has used up space: {disk_used / (1024 * 1024 * 1024):.2f} GB")
        info_handler(message = f"Remaining space in deployment directory: {disk_free_gb:.2f} GB")

    def check_memory(self):
        """
        Check the remaining memory. | 检查剩余内存
        """
        # 1. 获取系统内存信息

        # 获取服务器总内存大小
        mem_info = psutil.virtual_memory()
        # 获取剩余内存大小
        mem_free = mem_info.available
        # 转换为GB
        mem_free_gb = mem_free / (1024 * 1024 * 1024)
        if mem_free_gb < 8:
            error_handler(
                message = "There is not enough memory for WhaleStudio Deployment."
            )
            error_handler(message = "Deployment requires at least 8 GB of free memory.")
            error_handler(message = f"Current free memory: {mem_free_gb:.2f} GB.")
            sys.exit(1)
        debug_handler(message = f"Total memory: {mem_info.total / (1024 * 1024 * 1024):.2f} GB")
        debug_handler(message = f"Used memory: {mem_info.used / (1024 * 1024 * 1024):.2f} GB")
        info_handler(message = f"Remaining memory: {mem_free_gb:.2f} GB")
