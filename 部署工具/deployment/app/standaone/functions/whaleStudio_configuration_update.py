#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : configuration_file_update.py
# @Time    : 2025/06/04 09:43
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys
import traceback
import psutil
from app.common.utils import task_running_time, service_jvm_stack_size, service_jvm_young_generation_size
from app.common.logging_utils import info_handler, error_handler, debug_handler, warning_handler, title_handler, console
from app.config.deployment_config import deployment_config
from app.config.scheduler_config import scheduler_startup_config, other_files_to_sync
from app.config.whaleTunnel_config import whale_tunnel_config
from app.standaone.tools.file_tool import FileTool

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class ConfigurationFileUpdate:
    """
    This class is used to update the configuration file of the standalone program. | 该类用于更新standalone程序的配置文件
    """

    def __init__(self):

        whale_scheduler_current_path = os.path.join(
            deployment_config.deployment_dir, "current", "whalestudio"
        )

        self.scheduler_service_config_path = {
            "api": os.path.join(
                whale_scheduler_current_path, "api-server", "conf"
            ),
            "master": os.path.join(
                whale_scheduler_current_path, "master-server", "conf"
            ),
            "worker": os.path.join(
                whale_scheduler_current_path, "worker-server", "conf"
            ),
            "alert": os.path.join(
                whale_scheduler_current_path, "alert-server", "conf"
            )
        }

        self.scheduler_config_path = os.path.join(
            whale_scheduler_current_path, "bin", "env", "whalescheduler_env.sh"
        )
        self.whale_tunnel_config_path = os.path.join(
            deployment_config.deployment_dir, "current", "whaletunnel", "config"
        )

    @task_running_time(task_name = "Update Configuration File")
    def run(self):
        """
        This method is used to update the configuration file of the standalone program. | 该方法用于更新standalone程序的配置文件
        :return:
        """
        self.update_scheduler_config()
        if deployment_config.deploy_whaletunnel:
            self.update_whaleTunnel_config()

    def update_scheduler_config(self):
        """
        This method is used to update the scheduler configuration file. | 该方法用于更新scheduler配置文件
        :return:
        """
        title_handler(message = "Update Scheduler Configuration File".center(100, "="))
        with console.status("[bold green]Updating scheduler configuration file...[/bold green]"):
            for update_scheduler_config_task in [
                self._update_scheduler_startup_config,
                self._update_scheduler_jvm_config,
                self._sync_file_to_scheduler_config_dir,
            ]:
                update_scheduler_config_task()

    def update_whaleTunnel_config(self):
        """
        This method is used to update the whaleTunnel configuration file. | 该方法用于更新whaleTunnel配置文件
        :return:
        """
        title_handler(message = "Update WhaleTunnel Configuration File".center(100, "="))
        with console.status("[bold green]Updating whaleTunnel configuration file...[/bold green]"):
            for update_whaleTunnel_config_task in [
                self._update_whaleTunnel_config,
                self._update_whaleTunnel_jvm_config,
                self._update_hazelcast_config,
                self._update_hazelcast_client_config,
            ]:
                update_whaleTunnel_config_task()

    def _update_scheduler_startup_config(self):
        """
        This method is used to update the scheduler startup configuration file. | 该方法用于更新scheduler启动配置文件
        :return:
        """
        title_handler(message = "Update Scheduler Startup Configuration".center(80, "*"))
        write_status, write_message = FileTool.write_file(
            file_path = self.scheduler_config_path, content = scheduler_startup_config(), file_type = "txt"
        )
        if write_status:
            info_handler(message = "Update scheduler startup configuration file successfully.")
        else:
            error_handler(
                message = "Failed to update scheduler startup configuration file. Error message: {}".format(
                    write_message
                )
            )
            debug_handler(message = traceback.format_exc())
            sys.exit(1)

    def _update_scheduler_jvm_config(self):
        """
        This method is used to update the scheduler JVM configuration file. | 该方法用于更新scheduler JVM配置文件
        :return:
        """
        title_handler(message = "Update Scheduler JVM Configuration".center(80, "*"))
        for scheduler_service_name in list(self.scheduler_service_config_path.keys()):
            scheduler_service_jvm_size = deployment_config.roles_memory_size.get(scheduler_service_name, 2)
            scheduler_service_jvm_file_path = os.path.join(
                os.path.dirname(self.scheduler_service_config_path.get(scheduler_service_name)), "bin",
                "jvm_args_env.sh"
            )
            if not FileTool.is_file_exists(file_path = scheduler_service_jvm_file_path):
                warning_handler(
                    message = f"Scheduler {scheduler_service_name} JVM configuration file does not exist, skipping!"
                )
                continue

            # 获取当前jvm配置
            read_status, current_jvm_config = FileTool.read_file(
                file_path = scheduler_service_jvm_file_path, file_type = "txt"
            )
            if not read_status:
                error_handler(
                    message = f"Failed to read scheduler {scheduler_service_name} JVM configuration file. Error message: {current_jvm_config}"
                )
                continue
            current_jvm_config = [
                line.strip() for line in current_jvm_config if line.strip() and not line.strip().startswith("-Xm")
            ]

            current_jvm_config += [f"-Xms{service_jvm_stack_size(jvm_stack_size = scheduler_service_jvm_size)}",
                                   f"-Xmx{service_jvm_stack_size(jvm_stack_size = scheduler_service_jvm_size)}",
                                   f"-Xmn{service_jvm_young_generation_size(jvm_stack_size = scheduler_service_jvm_size)}"
                                   ]

            debug_handler(message = f"Scheduler {scheduler_service_name} JVM configuration: {current_jvm_config}")
            # 写入新的jvm配置
            write_status, write_message = FileTool.write_file(
                file_path = scheduler_service_jvm_file_path, content = "\n".join(current_jvm_config), file_type = "txt"
            )
            if write_status:
                info_handler(
                    message = f"Update scheduler {scheduler_service_name} JVM configuration file successfully."
                )
            else:
                error_handler(
                    message = f"Failed to update scheduler {scheduler_service_name} JVM configuration file. Error message: {write_message}"
                )
                debug_handler(message = traceback.format_exc())
                sys.exit(1)

    def _sync_file_to_scheduler_config_dir(self):
        """
        This method is used to synchronize the configuration file to the scheduler configuration directory. | 该方法用于将配置文件同步到scheduler配置目录
        :return:
        """
        other_files = other_files_to_sync()
        if not other_files:
            debug_handler(message = "No other files need to be synchronized to scheduler configuration directory.")
            return
        title_handler(message = "Synchronize Other Files to Scheduler Configuration Directory".center(80, "*"))
        for file in other_files:
            file_name = os.path.basename(file)
            for scheduler_service_name, scheduler_config_dir in self.scheduler_service_config_path.items():
                target_file_path = os.path.join(scheduler_config_dir, file_name)
                sync_status, sync_message = FileTool.sync_file(
                    src_file_path = file, dst_file_path = target_file_path
                )
                if sync_status:
                    info_handler(
                        message = f"Synchronize {file_name} to scheduler {scheduler_service_name} configuration directory successfully."
                    )
                else:
                    error_handler(
                        message = f"Failed to synchronize {file_name} to scheduler {scheduler_service_name} configuration directory. Error message: {sync_message}"
                    )
                    debug_handler(message = traceback.format_exc())
                    continue

    def _update_whaleTunnel_config(self):
        """
        This method is used to update the whaleTunnel configuration file. | 该方法用于更新whaleTunnel配置文件
        :return:
        """
        title_handler(message = "Update WhaleTunnel Configuration".center(80, "*"))

        whale_tunnel_config_path = os.path.join(self.whale_tunnel_config_path, "seatunnel.yaml")
        write_status, write_message = FileTool.write_file(
            file_path = whale_tunnel_config_path, content = whale_tunnel_config(), file_type = "yaml"
        )
        if write_status:
            info_handler(message = "Update whaleTunnel configuration file successfully.")
        else:
            error_handler(
                message = "Failed to update whaleTunnel configuration file. Error message: {}".format(
                    write_message
                )
            )
            debug_handler(message = traceback.format_exc())
            sys.exit(1)

    def _update_whaleTunnel_jvm_config(self):
        """
        This method is used to update the whaleTunnel JVM configuration file. | 该方法用于更新whaleTunnel JVM配置文件
        :return:
        """
        title_handler(message = "Update WhaleTunnel JVM Configuration".center(80, "*"))
        whale_tunnel_jvm_file_path = os.path.join(
            self.whale_tunnel_config_path, "jvm_options"
        )
        read_status, current_jvm_config = FileTool.read_file(
            file_path = whale_tunnel_jvm_file_path, file_type = "txt"
        )
        if not read_status:
            error_handler(
                message = "Failed to read whaleTunnel JVM configuration file. Error message: {}".format(
                    current_jvm_config
                )
            )
            sys.exit(1)
        current_jvm_config = [
            line.strip() for line in current_jvm_config if
            line.strip() and not line.strip().startswith(("-Xm", "-XX:ConcGCThreads"))
        ]

        whale_tunnel_jvm_size = service_jvm_stack_size(
            jvm_stack_size = deployment_config.roles_memory_size.get('whaletunnel', 8)
        )

        current_jvm_config += [
            f"-Xms{whale_tunnel_jvm_size}",
            f"-Xmx{whale_tunnel_jvm_size}"
        ]

        try:
            cpu_cores = psutil.cpu_count()
        except Exception as e:
            debug_handler(message = f"Failed to get CPU cores, use default value: {e}")
            debug_handler(message = traceback.format_exc())
            cpu_cores = 8

        if cpu_cores > 8:
            current_jvm_config.append(
                "-XX:ConcGCThreads=6"
            )
        else:
            current_jvm_config.append(
                f"-XX:ConcGCThreads={max(1, int(cpu_cores * 5 / 8))}"
            )

        debug_handler(message = f"WhaleTunnel JVM configuration: {current_jvm_config}")
        write_status, write_message = FileTool.write_file(
            file_path = whale_tunnel_jvm_file_path, content = "\n".join(current_jvm_config), file_type = "txt"
        )
        if write_status:
            info_handler(message = "Update whaleTunnel JVM configuration file successfully.")
        else:
            error_handler(
                message = "Failed to update whaleTunnel JVM configuration file. Error message: {}".format(
                    write_message
                )
            )
            debug_handler(message = traceback.format_exc())
            sys.exit(1)

    def _update_hazelcast_config(self):
        """
        This method is used to update the hazelcast configuration file. | 该方法用于更新hazelcast配置文件
        :return:
        """
        title_handler(message = "Update Hazelcast Configuration".center(80, "*"))
        hazelcast_config_path = os.path.join(self.whale_tunnel_config_path, "hazelcast.yaml")
        if not FileTool.is_file_exists(file_path = hazelcast_config_path):
            error_handler(message = "Hazelcast configuration file does not exist, skipping!")
            return
        read_status, current_hazelcast_config = FileTool.read_file(
            file_path = hazelcast_config_path, file_type = "yaml"
        )
        if not read_status:
            error_handler(
                message = "Failed to read hazelcast configuration file. Error message: {}".format(
                    current_hazelcast_config
                )
            )
            sys.exit(1)
        new_hazelcast_config = current_hazelcast_config.copy()
        new_hazelcast_config[
            "hazelcast"
        ][
            "network"
        ][
            "join"
        ][
            "tcp-ip"
        ][
            "member-list"
        ] = [
            deployment_config.local_ip
        ]
        debug_handler(message = f"Hazelcast configuration: {new_hazelcast_config}")
        write_status, write_message = FileTool.write_file(
            file_path = hazelcast_config_path, content = new_hazelcast_config, file_type = "yaml"
        )
        if write_status:
            info_handler(message = "Update hazelcast configuration file successfully.")
        else:
            error_handler(
                message = "Failed to update hazelcast configuration file. Error message: {}".format(
                    write_message
                )
            )
            debug_handler(message = traceback.format_exc())
            sys.exit(1)

    def _update_hazelcast_client_config(self):
        """
        This method is used to update the hazelcast client configuration file. | 该方法用于更新hazelcast client配置文件
        :return:
        """
        title_handler(message = "Update Hazelcast Client Configuration".center(80, "*"))
        hazelcast_client_config_path = os.path.join(self.whale_tunnel_config_path, "hazelcast-client.yaml")
        if not FileTool.is_file_exists(file_path = hazelcast_client_config_path):
            error_handler(message = "Hazelcast client configuration file does not exist, skipping!")
            return
        read_status, current_hazelcast_client_config = FileTool.read_file(
            file_path = hazelcast_client_config_path, file_type = "yaml"
        )
        new_hazelcast_client_config = current_hazelcast_client_config.copy()
        new_hazelcast_client_config[
            "hazelcast-client"
        ][
            "network"
        ][
            "cluster-members"
        ] = [
            f"{deployment_config.local_ip}:5801"
        ]
        debug_handler(message = f"Hazelcast client configuration: {new_hazelcast_client_config}")
        write_status, write_message = FileTool.write_file(
            file_path = hazelcast_client_config_path, content = new_hazelcast_client_config, file_type = "yaml"
        )
        if write_status:
            info_handler(message = "Update hazelcast client configuration file successfully.")
        else:
            error_handler(
                message = "Failed to update hazelcast client configuration file. Error message: {}".format(
                    write_message
                )
            )
            debug_handler(message = traceback.format_exc())
            sys.exit(1)
