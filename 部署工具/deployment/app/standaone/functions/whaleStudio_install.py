#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : install_for_standalone.py
# @Time    : 2025/06/03 17:19
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys
import tarfile
from datetime import datetime

from app.config.deployment_config import deployment_config
from app.common.utils import task_running_time
from app.common.package import get_latest_package
from app.common.logging_utils import info_handler, error_handler, debug_handler, warning_handler, title_handler, console
from app.standaone.functions.pre_check import PreDeploymentCheck
from app.standaone.functions.whaleStudio_configuration_update import ConfigurationFileUpdate
from app.standaone.tools.path_tool import PathTool
from app.standaone.tools.file_tool import FileTool

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class WhaleStudioInstaller:
    """
    Whale Studio 安装器
    """

    def __init__(self):
        # 1. 检查环境
        if not deployment_config.skip_pre_check:
            PreDeploymentCheck().run()

        # 2. 检查解压目录
        self.extract_dir = os.path.join(
            deployment_config.deployment_dir, "current_package"
        )

        # 软连接目录
        self.symlink_dir = os.path.join(
            deployment_config.deployment_dir, "current"
        )

        # 2. 获取最新安装包
        self.package_path, self.package_name, package_md5sum, package_size, package_size_str = get_latest_package()

    @task_running_time(task_name = "Installing Whale Studio for Standalone")
    def install(self):
        """
        Install the standalone environment. | 安装独立环境
        :return:
        """
        title_handler(message = "Installing Whale Studio".center(100, "="))
        for install_task in [
            # 检查解压目录
            self.check_extract_dir,
            self.extract_package,
            self.update_symlink,
            # 更新配置文件
        ]:
            install_task()
        ConfigurationFileUpdate().run()
        # 3. 解压安装包
        # 4. 记录当前软连接
        # 5. 删除旧的软连接
        # 6. 创建新的软连接

    def check_extract_dir(self):
        """
        检查解压目录
        :return:
        """
        if not PathTool.is_directory_exist(path = self.extract_dir):
            create_dir_status, create_dir_msg = PathTool.create_directory(path = self.extract_dir)
            if not create_dir_status:
                error_handler(message = f"Failed to create extract directory: {create_dir_msg}")
                sys.exit(1)
            debug_handler(message = f"Extract directory created: {self.extract_dir}")
            return
        debug_handler(message = f"Extract directory already exists: {self.extract_dir}")

    @task_running_time(task_name = "Extracting Whale Studio Package")
    def extract_package(self):
        """
        解压安装包
        :return:
        """
        title_handler(message = "Extracting Whale Studio Package".center(80, "*"))
        # 安装包解压路径
        with console.status(f"[bold green]Extracting package...[/bold green]"):
            self.extract_path = os.path.join(
                self.extract_dir,
                f"{self.package_name.split('.tar.gz')[0]}_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}"
            )
            create_dir_status, create_dir_msg = PathTool.create_directory(path = self.extract_path)
            if not create_dir_status:
                error_handler(message = f"Failed to create extract path: {create_dir_msg}")
                sys.exit(1)
            debug_handler(message = f"Extract path created: {self.extract_path}")

            try:
                with tarfile.open(self.package_path, "r:gz") as tar:
                    tar.extractall(self.extract_path)
                debug_handler(message = f"Package extracted to: {self.extract_path}")
            except Exception as e:
                error_handler(message = f"Failed to extract package: {e}")
                sys.exit(1)

            # 检查解压后的目录子目录是否为 [whalescheduler, whaletunnel, datasource]
            sub_dirs = os.listdir(self.extract_path)
            if not all(sub_dir in sub_dirs for sub_dir in ["whalescheduler", "whaletunnel", "datasource"]):
                error_handler(
                    message = f"Failed to extract package: {self.package_name} is not a valid Whale Studio package"
                )
                sys.exit(1)

            if not deployment_config.keep_package:
                debug_handler(f"{FileTool.delete_file(file_path = self.package_path)}")

        info_handler(message = f"Package extracted to: {self.extract_path}")

    @task_running_time(task_name = "Updating Symlink")
    def update_symlink(self):
        """
        更新软连接
        :return:
        """
        title_handler(message = "Updating Symlink".center(80, "*"))
        with console.status(f"[bold green]Updating symlink...[/bold green]"):
            for task in [
                self.record_current_symlink,
                self.create_new_symlink,
            ]:
                task()

    def record_current_symlink(self):
        """
        记录当前软连接
        :return:
        """
        if not PathTool.is_directory_exist(path = self.symlink_dir):
            warning_handler(message = f"First deployment, no need to record soft connections.")
            return

        # 获取所有的子目录
        sub_dirs = os.listdir(self.symlink_dir)
        if not sub_dirs:
            warning_handler(message = f"First deployment, no need to record soft connections.")
            return

        # 获取包含在 [whalestudio, whaletunnel, datasource] 的子目录
        valid_sub_dirs = [os.path.join(self.symlink_dir, sub_dir) for sub_dir in sub_dirs if
                          sub_dir in ["whalestudio", "whaletunnel", "datasource"]]
        if not valid_sub_dirs:
            warning_handler(message = f"First deployment, no need to record soft connections.")
            return
        # 获取子目录是否是软连接以及对应关系
        symlink_info = PathTool.get_symlink_target(sub_paths = valid_sub_dirs)
        if symlink_info:
            debug_handler(message = f"Current soft connections: {symlink_info}")
            FileTool.write_file(
                file_path = os.path.join(CURRENT_DIRECTORY, "current_symlink.json"),
                content = symlink_info,
                file_type = "json"
            )
            # 删除旧的软连接
            self.delete_old_symlink(
                symlink_info = symlink_info
            )
        else:
            warning_handler(message = f"First deployment, no need to record soft connections.")

    def delete_old_symlink(self, symlink_info):
        """
        删除旧的软连接
        :return:
        """
        title_handler(message = "Deleting Old Symlink".center(80, "*"))
        for symlink_path, _ in symlink_info.items():
            delete_symlink_status, delete_symlink_msg = PathTool.delete_symlink(symlink_path = symlink_path)
            if not delete_symlink_status:
                error_handler(message = f"Failed to delete old symlink: {delete_symlink_msg}")
                sys.exit(1)
            info_handler(message = f"symlink {symlink_path} deleted successfully.")

    def create_new_symlink(self):
        """
        创建新的软连接
        :return:
        """
        title_handler(message = "Creating New Symlink".center(80, "*"))
        # 新的软连接对应关系
        if not PathTool.is_directory_exist(
                path = self.symlink_dir
        ):
            create_dir_status, create_dir_msg = PathTool.create_directory(path = self.symlink_dir)
            if not create_dir_status:
                error_handler(message = f"Failed to create symlink directory: {create_dir_msg}")
                sys.exit(1)
            debug_handler(message = f"Symlink directory created: {self.symlink_dir}")
        new_symlink_info = {
            os.path.join(self.symlink_dir, "whalestudio"): os.path.join(self.extract_path, "whalescheduler"),
            os.path.join(self.symlink_dir, "datasource"): os.path.join(self.extract_path, "datasource"),
        }
        if deployment_config.deploy_whaletunnel:
            new_symlink_info[os.path.join(self.symlink_dir, "whaletunnel")] = os.path.join(
                self.extract_path, "whaletunnel"
            )
        for symlink_path, target_path in new_symlink_info.items():
            create_symlink_status, create_symlink_msg = PathTool.create_symlink(
                symlink_path = symlink_path,
                target_path = target_path
            )
            if not create_symlink_status:
                error_handler(message = f"Failed to create new symlink: {create_symlink_msg}")
                sys.exit(1)
            info_handler(message = f"symlink {symlink_path} created successfully.")
            # 记录新的软连接
