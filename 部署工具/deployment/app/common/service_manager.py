#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : service_manager.py
# @Time    : 2025/07/25 
# <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

"""
统一的服务管理抽象层
Unified service management abstraction layer
"""

import time
from abc import ABC, abstractmethod
from typing import List, Dict, Tuple, Optional
from app.config.deployment_config import deployment_config
from app.cluster.config.cluster_node_config import get_services_by_ip
from app.common.logging_utils import debug_handler, info_handler, error_handler, warning_handler


class ServiceManager(ABC):
    """
    服务管理器抽象基类
    Abstract base class for service managers
    """
    
    def __init__(self, host: str, **kwargs):
        self.host = host
        self.kwargs = kwargs
        self.operation_type = kwargs.get("operation_type", None)
        self.result_dict = kwargs.get("result_dict", {})
        
        # 处理服务列表
        self._process_service_list()
    
    def _process_service_list(self):
        """
        处理服务列表，根据节点角色配置过滤
        Process service list based on node roles configuration
        """
        user_input_services = self.kwargs.get("user_input_services", [])
        use_node_roles = self.kwargs.get("use_node_roles", False)
        node_services = get_services_by_ip(self.host)
        
        if use_node_roles and user_input_services == "all":
            # 当用户输入 "all" 且需要根据节点角色过滤时，使用节点的角色配置
            self.target_services = node_services
            debug_handler(
                message = f"Host: {self.host} - Using node roles configuration: {self.target_services}"
            )
        elif isinstance(user_input_services, list):
            # 用户指定了具体服务，需要与节点角色配置取交集
            self.target_services = [
                service for service in user_input_services 
                if service in node_services
            ]
            
            # 记录被过滤掉的服务
            filtered_services = set(user_input_services) - set(self.target_services)
            if filtered_services:
                debug_handler(
                    message = f"Host: {self.host} - Services filtered out (not in node roles): {list(filtered_services)}"
                )
            
            debug_handler(
                message = f"Host: {self.host} - Final service list after role filtering: {self.target_services}"
            )
        else:
            # 默认情况，使用节点配置的所有服务
            self.target_services = node_services
    
    def execute_operation(self) -> Tuple[bool, str]:
        """
        执行服务操作
        Execute service operation
        """
        start_time = time.time()
        
        try:
            result = self._execute_operation_impl()
            
            # 记录执行时间
            end_time = time.time()
            elapsed_time = end_time - start_time
            
            if self.host in self.result_dict:
                if elapsed_time > 60:
                    self.result_dict[self.host].append(
                        f"{self.operation_type} operation time: {elapsed_time / 60:.2f} minutes"
                    )
                else:
                    self.result_dict[self.host].append(
                        f"{self.operation_type} operation time: {elapsed_time:.2f} seconds"
                    )
            
            return result
            
        except Exception as e:
            error_handler(message = f"Error executing {self.operation_type} on {self.host}: {e}")
            return False, str(e)
    
    @abstractmethod
    def _execute_operation_impl(self) -> Tuple[bool, str]:
        """
        具体的操作实现，由子类实现
        Specific operation implementation, to be implemented by subclasses
        """
        pass
    
    @abstractmethod
    def get_service_status(self, service_name: str) -> Optional[List[str]]:
        """
        获取服务状态（进程ID列表）
        Get service status (list of process IDs)
        """
        pass
    
    @abstractmethod
    def start_service(self, service_name: str) -> Tuple[bool, str]:
        """
        启动服务
        Start service
        """
        pass
    
    @abstractmethod
    def stop_service(self, service_name: str) -> Tuple[bool, str]:
        """
        停止服务
        Stop service
        """
        pass
    
    def restart_service(self, service_name: str) -> Tuple[bool, str]:
        """
        重启服务（默认实现：先停止再启动）
        Restart service (default implementation: stop then start)
        """
        # 先停止服务
        pids = self.get_service_status(service_name)
        if pids:
            stop_result = self.stop_service(service_name)
            if not stop_result[0]:
                return stop_result
            
            # 等待一下确保服务完全停止
            time.sleep(2)
        
        # 启动服务
        return self.start_service(service_name)


class ServiceOperationFactory:
    """
    服务操作工厂类
    Service operation factory class
    """
    
    @staticmethod
    def create_start_operation(manager: ServiceManager) -> 'ServiceOperation':
        return StartServiceOperation(manager)
    
    @staticmethod
    def create_stop_operation(manager: ServiceManager) -> 'ServiceOperation':
        return StopServiceOperation(manager)
    
    @staticmethod
    def create_restart_operation(manager: ServiceManager) -> 'ServiceOperation':
        return RestartServiceOperation(manager)
    
    @staticmethod
    def create_status_operation(manager: ServiceManager) -> 'ServiceOperation':
        return StatusServiceOperation(manager)


class ServiceOperation(ABC):
    """
    服务操作抽象基类
    Abstract base class for service operations
    """
    
    def __init__(self, manager: ServiceManager):
        self.manager = manager
    
    @abstractmethod
    def execute(self) -> Tuple[bool, str]:
        """
        执行操作
        Execute operation
        """
        pass


class StartServiceOperation(ServiceOperation):
    """启动服务操作"""
    
    def execute(self) -> Tuple[bool, str]:
        success_count = 0
        total_count = len(self.manager.target_services)
        
        for service in self.manager.target_services:
            # 检查服务是否已经运行
            if self.manager.get_service_status(service):
                warning_handler(
                    message = f"Host: {self.manager.host} service: `{service}` is already running, skipping start operation."
                )
                success_count += 1
                continue
            
            # 启动服务
            result = self.manager.start_service(service)
            if result[0]:
                success_count += 1
                info_handler(message = f"Host: {self.manager.host} service: `{service}` started successfully.")
            else:
                error_handler(message = f"Host: {self.manager.host} service: `{service}` start failed: {result[1]}")
        
        if success_count == total_count:
            return True, f"All {total_count} services started successfully"
        else:
            return False, f"Only {success_count}/{total_count} services started successfully"


class StopServiceOperation(ServiceOperation):
    """停止服务操作"""
    
    def execute(self) -> Tuple[bool, str]:
        success_count = 0
        total_count = len(self.manager.target_services)
        
        for service in self.manager.target_services:
            # 检查服务是否正在运行
            if not self.manager.get_service_status(service):
                warning_handler(
                    message = f"Host: {self.manager.host} service: `{service}` is not running, skipping stop operation."
                )
                success_count += 1
                continue
            
            # 停止服务
            result = self.manager.stop_service(service)
            if result[0]:
                success_count += 1
                info_handler(message = f"Host: {self.manager.host} service: `{service}` stopped successfully.")
            else:
                error_handler(message = f"Host: {self.manager.host} service: `{service}` stop failed: {result[1]}")
        
        if success_count == total_count:
            return True, f"All {total_count} services stopped successfully"
        else:
            return False, f"Only {success_count}/{total_count} services stopped successfully"


class RestartServiceOperation(ServiceOperation):
    """重启服务操作"""
    
    def execute(self) -> Tuple[bool, str]:
        success_count = 0
        total_count = len(self.manager.target_services)
        
        for service in self.manager.target_services:
            result = self.manager.restart_service(service)
            if result[0]:
                success_count += 1
                info_handler(message = f"Host: {self.manager.host} service: `{service}` restarted successfully.")
            else:
                error_handler(message = f"Host: {self.manager.host} service: `{service}` restart failed: {result[1]}")
        
        if success_count == total_count:
            return True, f"All {total_count} services restarted successfully"
        else:
            return False, f"Only {success_count}/{total_count} services restarted successfully"


class StatusServiceOperation(ServiceOperation):
    """状态查询操作"""
    
    def execute(self) -> Tuple[bool, str]:
        status_info = []
        
        for service in self.manager.target_services:
            pids = self.manager.get_service_status(service)
            if pids:
                status_info.append(f"{service}: running (PIDs: {', '.join(pids)})")
            else:
                status_info.append(f"{service}: stopped")
        
        return True, f"Host: {self.manager.host} service status:\n" + "\n".join(status_info)
