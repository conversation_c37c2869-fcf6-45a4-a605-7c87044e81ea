#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : package.py
# @Time    : 2025/06/03 18:37
# <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import sys
import traceback
import hashlib
from glob import glob
from app.config.deployment_config import deployment_config
from app.common.logging_utils import error_handler, info_handler, warning_handler, debug_handler, title_handler, console
from app.common.utils import task_running_time

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


@task_running_time(task_name = "Get latest package")
def get_latest_package():
    """
    Get the latest package from the current directory. | 从当前目录获取最新的包
    :return:
    """
    title_handler(message = "Get latest package".center(100, "="))
    with console.status("[bold green]Getting latest package...[/bold green]"):

        if not os.path.isdir(deployment_config.package_dir):
            error_handler(message = f"Package directory {deployment_config.package_dir} does not exist.")
            warning_handler(
                message = f"Please create the {deployment_config.package_dir} directory and place the latest package into it."
            )
            sys.exit(1)

        packages = glob(os.path.join(deployment_config.package_dir, "*.tar.gz"))
        if not packages:
            error_handler(message = f"No package found in {deployment_config.package_dir}.")
            warning_handler(message = f"Please place the latest package into {deployment_config.package_dir}.")
            sys.exit(1)

        latest_package = max(packages, key = os.path.getctime)
        info_handler(message = f"Latest package: {latest_package}")
        latest_package_md5sum = get_package_md5sum(package_path = latest_package)
        info_handler(message = f"Latest package md5sum: {latest_package_md5sum}")
        latest_package_size, latest_package_size_str = get_package_size(package_path = latest_package)
        info_handler(message = f"Latest package size: {latest_package_size_str}")
        return latest_package, os.path.basename(
            latest_package
        ), latest_package_md5sum, latest_package_size, latest_package_size_str


def get_package_md5sum(package_path):
    """
    Get the md5sum of the package. | 获取包的md5sum
    :param package_path:
    :return:
    """
    try:
        md5 = hashlib.md5()
        with open(package_path, "rb") as f:
            while True:
                data = f.read(409600000)  # 409600000 bytes = 400 MB
                if not data:
                    break
                md5.update(data)
        hex_digest = md5.hexdigest()
        debug_handler(message = f"MD5 of {package_path}: {hex_digest}")
        return md5.hexdigest()
    except ImportError:
        error_handler(message = "Please install hashlib module to get the md5sum of the package.")
        sys.exit(1)
    except Exception as e:
        error_handler(message = f"Failed to get the md5sum of the package: {e}")
        debug_handler(message = traceback.format_exc())
        sys.exit(1)


def get_package_size(package_path):
    """
    Get the size of the package. | 获取包的大小
    :param package_path:
    :return:
    """
    try:
        size = os.path.getsize(package_path)
        debug_handler(message = f"Size of {package_path}: {size} bytes")
        if size < 1024:
            size_cost = f"{size} bytes"

        elif size < 1024 * 1024:
            # Convert to KB
            size_cost = f"{size / 1024:.2f} KB"

        elif size < 1024 * 1024 * 1024:
            # Convert to MB
            size_cost = f"{size / 1024 / 1024:.2f} MB"
        else:
            # Convert to GB
            size_cost = f"{size / 1024 / 1024 / 1024:.2f} GB"
        return size, size_cost
    except Exception as e:
        error_handler(message = f"Failed to get the size of the package: {e}")
        debug_handler(message = traceback.format_exc())
        sys.exit(1)
