#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : utils.py
# @Time    : 2025/06/03 16:46
# <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import re
import sys
import pwd
import time
from rich.console import Console
from functools import wraps

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


def get_parent_directory(path, level = 1):
    try:
        for i in range(level):
            path = os.path.dirname(path)
        return path
    except Exception as e:
        print(f"Error getting parent directory: {e}")
        return None


# 脚本启动用户
def startup_user():
    """
    Get the user who started the script. | 获取脚本启动用户
    :return:
    """
    try:
        user_id = os.getuid()
        user_name = pwd.getpwuid(user_id).pw_name
        if user_name == 'root':
            return True, user_name
        else:
            return False, user_name
    except Exception as e:
        return False, None


def task_running_time(task_name = ""):
    """
    Get the running time of a task. | 获取某个任务的运行时间
    :param task_name:
    :return:
    """

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)  # 调用原始函数
            end_time = time.time()
            elapsed_time = end_time - start_time
            if elapsed_time > 60:
                elapsed_time = elapsed_time / 60
                unit = "minutes"
            else:
                unit = "seconds"
            Console().print(f"[bold yellow]Task: {task_name} running time: {elapsed_time:.2f} {unit}[/bold yellow]")
            return result

        return wrapper

    return decorator


def escape_special_characters(string):
    """
    Escape special characters in a string. | 对字符串中的特殊字符进行转义
    """
    return string.replace('"', '\\"')


def service_jvm_stack_size(jvm_stack_size = 2):
    """
    Set the JVM stack size for a service. | 设置服务的JVM堆栈大小
    :param jvm_stack_size: The size of the JVM stack in megabytes. | JVM堆栈大小，单位为兆
    :return:
    """
    if isinstance(jvm_stack_size, int) or isinstance(jvm_stack_size, str):
        return f"{jvm_stack_size}g"
    elif isinstance(jvm_stack_size, float):

        return f"{int(jvm_stack_size * 1024)}m"


def service_jvm_young_generation_size(jvm_stack_size = 2):
    """
    Set the JVM young generation size for a service. | 设置服务的JVM年轻代大小
    :param jvm_stack_size:
    :return:
    """
    young_generation_size = jvm_stack_size * 1024 * 0.5
    if young_generation_size < 1024:
        return f"{int(young_generation_size)}m"
    else:
        return f"{int(young_generation_size / 1024)}g"


# 字符串转换为变量名称
def string_to_variable_name(string):
    """
    Convert a string to a valid variable name. | 将字符串转换为有效的变量名称
    :param string:
    :return:
    """
    return re.sub(
        '[.-]',
        '_',
        string.strip().upper()
    )
