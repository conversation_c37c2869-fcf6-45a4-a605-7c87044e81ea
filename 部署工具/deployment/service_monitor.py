#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : service_monitor_pool.py
# @Time    : 2025/06/13 14:53
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0
import json
import os
import sys
import argparse
import logging
import threading
import multiprocessing
import traceback
import requests
import uvicorn
from functools import partial
from datetime import datetime

import yaml
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from rich.console import Console
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from fastapi.staticfiles import StaticFiles
from loguru import logger
from apscheduler.schedulers.blocking import BlockingScheduler
from app.config.deployment_config import deployment_config, service_operation_command
from app.cluster.config.cluster_node_config import get_services_by_ip
from app.standaone.tools.command_execution_tools import execute_command
from app.cluster.cluster_for_ssh.tools.command_execution import execute_command as execute_ssh_command

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))

console = Console()

service_monitor_scheduler = BlockingScheduler()

if deployment_config.deployment_mode == "standalone":
    # 服务监控结果
    service_monitor_result = {
        "whalestudio_mode": "standalone",
        "service": {}
    }
else:
    service_monitor_result = {
        "whalestudio_mode": "cluster",
        "nodes": {}
    }

app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins = ["*"],  # 允许所有来源，生产环境中应该限制为实际的前端域名
    allow_credentials = True,
    allow_methods = ["*"],  # 允许所有方法
    allow_headers = ["*"],  # 允许所有头部
)


def setup_logger(logs_path = None, log_level = "INFO"):
    """Setup the logger. | 设置日志记录器"""
    if not logs_path:
        logs_path = os.path.join(CURRENT_DIRECTORY, "logs", "WhaleStudioMonitor.log")
    if not hasattr(setup_logger, 'has_configured'):
        logger.remove()
        logger.add(
            logs_path, format = "{time} | {level} | {message}", mode = 'a',
            encoding = 'utf-8', level = log_level.upper(), rotation = "20 MB"
        )
        setattr(setup_logger, 'has_configured', True)

    class InterceptHandler(logging.Handler):
        def emit(self, record):
            try:
                level = logger.level(record.levelname).name
            except ValueError:
                level = logging.getLevelName(record.levelno)
            frame, depth = logging.currentframe(), 2
            while frame.f_code.co_filename == logging.__file__:
                frame = frame.f_back
                depth += 1
            logger.bind(request_id = None).opt(depth = depth, exception = record.exc_info).log(
                level, record.getMessage()
            )

    logging.basicConfig(handlers = [InterceptHandler()], level = log_level.upper())
    logging.getLogger("uvicorn").handlers = [InterceptHandler()]


def error_handler(message):
    """
    Error handler for loguru logger.
    """
    console.print(f"[bold red]{message}[/bold red] ")
    logger.error(message)


def info_handler(message):
    """
    Info handler for loguru logger.
    """
    logger.info(message)
    console.print(f"[bold green]{message}[/bold green]")


def debug_handler(message):
    """
    Debug handler for loguru logger.
    """
    logger.debug(message)


def warning_handler(message):
    """
    Warning handler for loguru logger.
    """
    console.print(f"[bold yellow]{message}[/bold yellow] ")
    logger.warning(message)


def title_handler(message):
    """
    Title handler for loguru logger.
    """
    console.print(f"[bold blue]{message}[/bold blue]")
    logger.info(message)


def start_service_monitor(interval: int = 300):
    """
    Start the service monitor. | 启动服务监控
    :param interval: 定时器间隔时间（秒），默认 5 分钟
    :return:
    """
    info_handler(message = f"Starting the service monitor with interval {interval} seconds...")
    # 检查间隔是否小于最小间隔（3 分钟）
    # if interval < 180:
    #     warning_handler(message = "The interval is too small! Minimum interval is 3 minutes.")
    #     interval = 180

    # 启动定时器
    info_handler(message = f"Starting the service monitor scheduler with interval {interval} seconds...")
    service_monitor_scheduler.add_job(
        func = monitor_service, trigger = "interval", seconds = interval, id = "service_monitor"
    )
    try:
        service_monitor_scheduler.start()
        info_handler(
            message = f"The service monitor scheduler has been started successfully! Interval: {interval} seconds."
        )
    except Exception as e:
        error_handler(message = f"Failed to start the service monitor scheduler. Error: {e}")
        error_handler(message = "Please check the logs for more information.")
        sys.exit(1)


@app.get("/api/monitor/task")
async def get_monitor_task():
    """
    Get the monitor task. | 获取监控任务
    :return:
    """
    if deployment_config.deployment_mode == "standalone" and not os.path.isdir(
            os.path.join(
                deployment_config.deployment_dir, "current", "whalestudio"
            )
    ):
        return {
            "status": "stop",
            "error": "请先部署WhaleStudio服务！"
        }

    else:
        if not service_monitor_scheduler.running:
            info_handler(message = "The service monitor scheduler is not running!")
            return {
                "status": "stop",
                "error": None
            }
        job_next_run_time = service_monitor_scheduler.get_job(job_id = "service_monitor").next_run_time
        if job_next_run_time:
            info_handler(message = "The service monitor scheduler is running!")
            info_handler(message = f"Task next run time: {job_next_run_time}")
            return {
                "status": "start",
                "error": None
            }
        elif job_next_run_time is None:
            warning_handler(message = "The service monitor scheduler is paused!")
            return {
                "status": "pause",
                "error": None
            }
        else:
            error_handler(message = "The service monitor scheduler is in an unknown state!")
            return {
                "status": "error",
                "error": "Unknown error"
            }


class MonitorStatus(BaseModel):
    action: str


@app.put("/api/monitor/task")
async def update_monitor_task(monitor_status: MonitorStatus):
    """
    Update the monitor task. | 更新监控任务
    :param request:
    :return:
    """
    warning_handler(message = f"Received monitor status update request: {monitor_status.action}")
    match monitor_status.action:
        case "start":
            if service_monitor_scheduler.running:
                return {
                    "status": "start",
                }
            else:
                return {
                    "status": service_monitor_scheduler.state
                }
        case "stop":
            if service_monitor_scheduler.running:
                service_monitor_scheduler.pause_job(job_id = "service_monitor")
                return {
                    "status": "stop"
                }
            else:
                return {
                    "status": "stop"
                }
        case "pause":
            # 暂停job
            warning_handler(message = "Received monitor pause request!")
            try:
                if service_monitor_scheduler.get_job(job_id = "service_monitor").next_run_time:
                    service_monitor_scheduler.pause_job(job_id = "service_monitor")
                    warning_handler(message = "The service monitor has been paused successfully!")
                    warning_handler(
                        message = f"Task next run time: {service_monitor_scheduler.get_job(job_id = 'service_monitor').next_run_time}"
                    )
                return {
                    "status": "pause"
                }
            except Exception as e:
                error_handler(message = f"Failed to pause the service monitor. Error: {e}")
                return {
                    "status": service_monitor_scheduler.running
                }
        case "resume":
            # 恢复job
            warning_handler(message = "Received monitor resume request!")
            try:
                if not service_monitor_scheduler.get_job(job_id = "service_monitor").next_run_time:
                    service_monitor_scheduler.resume_job(job_id = "service_monitor")
                    warning_handler(message = "The service monitor has been resumed successfully!")
                    warning_handler(
                        message = f"Task next run time: {service_monitor_scheduler.get_job(job_id = 'service_monitor').next_run_time}"
                    )
                return {
                    "status": "resume"
                }
            except Exception as e:
                error_handler(message = f"Failed to resume the service monitor. Error: {e}")
                return {
                    "status": service_monitor_scheduler.running
                }
        case _:
            return {
                "status": "Invalid action"
            }


@app.get("/api/service/status")
async def get_service_status():
    """
    Get the service status. | 获取服务状态
    :return:
    """
    debug_handler(
        message = f"Service monitor result: {service_monitor_scheduler.get_job(job_id = 'service_monitor').next_run_time}"
    )
    return service_monitor_result


@app.get("/api/service/reload")
async def reload_service():
    """
    Reload the service. | 重载服务
    :return:
    """
    warning_handler(message = "Received service reload request!")
    monitor_service()
    return service_monitor_result


def start_service(ui_port = 8000, interval = 300):
    """启动服务"""
    # 判断定时器是否已经启动
    if service_monitor_scheduler.running:
        warning_handler(message = "The service monitor scheduler is already running!")
        return
    # 获取PID并记录
    pid = os.getpid()
    try:
        with open(os.path.join(CURRENT_DIRECTORY, "service_monitor.pid"), "w") as f:
            f.write(str(pid))
    except Exception as e:
        error_handler(message = f"Failed to record PID file. Error: {e}")
        error_handler(message = "Please manually delete the service.pid file and restart the service!")

    ui_path = os.path.join(CURRENT_DIRECTORY, "app", "service_monitor_ui")
    # 检查UI路径是否存在
    if os.path.isdir(ui_path):
        # 挂载静态资源
        if os.path.isdir(os.path.join(ui_path, "static")):
            app.mount("/static", StaticFiles(directory = os.path.join(ui_path, "static")), name = "static")

        # 处理前端路由
        @app.get("/{full_path:path}")
        async def serve_react_app(full_path: str):
            # 如果路径以api开头，让FastAPI处理，否则返回index.html
            if full_path.startswith("api/"):
                raise HTTPException(status_code = 404, detail = "API Not Found")

            # 如果是静态资源请求，直接返回404让浏览器重新请求/static路径
            if full_path.startswith("static/"):
                raise HTTPException(status_code = 404, detail = "Use /static/ path instead")

            index_file = os.path.join(ui_path, "index.html")
            if os.path.exists(index_file):
                return FileResponse(index_file)
            else:
                raise HTTPException(status_code = 404, detail = "Frontend not built")

        # 处理根路径
        @app.get("/")
        async def serve_index():
            index_file = os.path.join(ui_path, "index.html")
            if os.path.exists(index_file):
                return FileResponse(index_file)
            else:
                raise HTTPException(status_code = 404, detail = "Frontend not built")
    else:
        # 如果UI路径不存在，则只提供API信息
        @app.get("/")
        async def serve_api_info():
            return {"message": "WhaleStudio 服务监控API",
                    "endpoints": ["/api/service/status", "/api/mode", "/api/monitor/task"]}
    try:
        # 使用 threading 模块启动服务监控定时器
        threading.Thread(target = start_service_monitor, args = (interval,), daemon = True).start()
        # 启动 FastAPI 应用
        uvicorn.run(app, host = "0.0.0.0", port = ui_port, log_level = "info")
    except KeyboardInterrupt:
        sys.exit(0)


def stop_service():
    """Stop the service. | 停止服务"""
    # 停止此脚本
    if not os.path.exists(os.path.join(CURRENT_DIRECTORY, "service_monitor.pid")):
        warning_handler(message = "Failed to retrieve PID file, service monitoring may not have started!")
        warning_handler(
            message = "If you cannot determine whether the service monitoring has been started, execute the ` ps -ef | grep service_monitor | grep -v grep ` command to check if the service monitoring process exists. If it exists, execute the 'kill -9 PID' command to kill the process."
        )
        return
    with open(os.path.join(CURRENT_DIRECTORY, "service_monitor.pid"), "r") as f:
        pid = int(f.read())
    try:
        os.kill(pid, 9)
        os.remove(os.path.join(CURRENT_DIRECTORY, "service_monitor.pid"))
        info_handler(message = "The service monitor has been stopped successfully!")
    except Exception as e:
        error_handler(message = f"Failed to stop the service monitor. Error: {e}")
        error_handler(message = "Please manually delete the service.pid file and restart the service!")
        return


class AlertSend:
    def __init__(self, config, content):
        """
        初始化告警发送类
        """
        self.config = config
        self.content = content

    def send_email(self):
        """
        发送邮件
        """
        from email.mime.text import MIMEText
        from email.header import Header
        import smtplib
        msg = MIMEText(self.content, 'plain', 'utf-8')
        # 发件人
        msg['From'] = Header(self.config.get("email", {}).get("sender_email", "<EMAIL>"))
        # 收件人
        to_mail = self.config.get("email", {}).get("receiver_email", [])
        if isinstance(to_mail, str):
            to_mail = [
                mail.strip() for mail in to_mail.split(",")
            ]
        elif isinstance(to_mail, list):
            to_mail = [
                mail.strip() for mail in to_mail
            ]
        else:
            error_handler(message = "Invalid receiver email address.")
            return
        msg['To'] = Header(",".join(to_mail))
        # 主题
        msg['Subject'] = Header(self.config.get("email", {}).get("subject", "WhaleStudio Service Monitor Alert"))
        # 发送邮件
        # 邮件服务器地址
        smtp_server = self.config.get("email", {}).get("smtp_server", "smtp.example.com")
        # 邮件服务器端口
        smtp_port = self.config.get("email", {}).get("smtp_port", 25)
        # 邮件服务器用户名
        smtp_user = self.config.get("email", {}).get("smtp_user", "username")
        # 邮件服务器密码
        smtp_password = self.config.get("email", {}).get("smtp_password", "password")
        try:
            smtp_obj = smtplib.SMTP(smtp_server, smtp_port, timeout = 20)
            # 启用SMTP认证
            smtp_obj.login(smtp_user, smtp_password)
            smtp_obj.sendmail(msg['From'], to_mail, msg.as_string())
            smtp_obj.quit()
            info_handler(message = "Email sent successfully.")
        except smtplib.SMTPException as e:
            error_handler(message = f"Failed to send email. Error: {e}")
        except Exception as e:
            error_handler(message = f"Failed to send email. Error: {e}")

    def send_feishu(self):
        """
        发送飞书通知
        """
        feishu_webhook = self.config.get("feishu", {}).get("webhook", "")
        if not feishu_webhook:
            error_handler(message = "No Feishu webhook found.")
            return
        headers = {
            "Content-Type": "application/json"
        }
        data = {
            "msg_type": "text",
            "content": {
                "text": f"{self.config.get('title', 'WhaleStudio Service Monitor')} - {self.content}"
            }
        }
        try:
            response = requests.post(url = feishu_webhook, headers = headers, json = data)
            if response.status_code == 200:
                info_handler(message = "Feishu notification sent successfully.")
            else:
                error_handler(message = f"Failed to send Feishu notification. Error: {response.text}")
        except Exception as e:
            error_handler(message = f"Failed to send Feishu notification. Error: {e}")

    def send_wecom(self):
        """
        发送企业微信通知
        """
        wecom_webhook = self.config.get("wecom", {}).get("webhook", "")
        if not wecom_webhook:
            error_handler(message = "No Wecom webhook found.")
            return
        headers = {
            "Content-Type": "application/json"
        }
        data = {
            "msgtype": "text",
            "text": {
                "content": f"{self.config.get('title', 'WhaleStudio Service Monitor')} - {self.content}"
            }
        }
        try:
            response = requests.post(url = wecom_webhook, headers = headers, json = data)
            if response.status_code == 200:
                info_handler(message = "Wecom notification sent successfully.")
            else:
                error_handler(message = f"Failed to send Wecom notification. Error: {response.text}")
        except Exception as e:
            error_handler(message = f"Failed to send Wecom notification. Error: {e}")


def send_alert_message(stop_service):
    """
    发送告警信息
    """
    if not os.path.join(CURRENT_DIRECTORY, "config", "service_monitor.yaml"):
        debug_handler(message = "No alert configuration found.")
        return

    try:
        with open(os.path.join(CURRENT_DIRECTORY, "config", "service_monitor.yaml"), "r") as f:
            alert_config = yaml.safe_load(f)
    except Exception as e:
        error_handler(message = f"Failed to load alert configuration. Error: {e}")
        return

    if not alert_config.get("send_notification", False):
        debug_handler(message = "No alert configuration found.")
        return

    alert_sender = AlertSend(
        config = alert_config,
        content = f'{",".join(stop_service)} 服务于{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}停止运行'
    )

    match alert_config.get("alert_method", "email").lower():
        case "email":
            # 发送邮件
            alert_sender.send_email()
        case "feishu":
            # 发送飞书通知
            alert_sender.send_feishu()
        case "wecom":
            # 发送企业微信通知
            alert_sender.send_wecom()
        case _:
            error_handler(message = "Invalid alert method.")
            return


class StandaloneServiceStatus:
    """
    Standalone service status. | 单机模式服务状态
    """

    def __init__(self):
        self.service_names = ["api", "master", "worker", "alert"]
        if deployment_config.deploy_whaletunnel:
            self.service_names.append("whaletunnel")

    def get_service_status(self):
        """
        Get the service status. | 获取服务状态
        :return:
        """
        start_services = []
        stop_service = []
        for service_name in self.service_names:
            service_status_command = service_operation_command(
                service_name = service_name, operation_name = "status"
            )
            debug_handler(message = f"Executing command: {service_status_command}")
            try:
                service_status_output = execute_command(service_status_command)
                debug_handler(message = f"Command output: {service_status_output}")
                if service_status_output[1]:
                    info_handler(message = f"Service {service_name} is running.")
                    start_services.append(service_name)
                else:
                    error_handler(message = f"Service {service_name} is not running.")
                    stop_service.append(service_name)
            except Exception as e:
                error_handler(message = f"Failed to execute command: {service_status_command}. Error: {e}")
                continue
        return start_services, stop_service

    def service_monitor(self):
        """
        Get the service status. | 获取服务状态
        :return:
        """
        start_services, stop_service = self.get_service_status()
        for service_name in start_services:
            if service_name not in service_monitor_result["service"]:
                service_monitor_result["service"][service_name] = {
                    "status": "start",
                    "restart_count": 0
                }
            else:
                service_monitor_result["service"][service_name]["status"] = "start"

        for service_name in stop_service:
            warning_handler(message = f"Service {service_name} is not running. Restarting...")
            if service_name not in service_monitor_result["service"]:
                service_monitor_result["service"][service_name] = {
                    "status": "stop",
                    "restart_count": 1
                }
            else:
                service_monitor_result["service"][service_name]["status"] = "stop"
                service_monitor_result["service"][service_name]["restart_count"] += 1

            service_restart_command = service_operation_command(
                service_name = service_name, operation_name = "start"
            )

            warning_handler(message = f"Executing command: {service_restart_command}")
            try:
                service_restart_output = execute_command(service_restart_command)
                warning_handler(message = f"Command output: {service_restart_output}")
                if service_restart_output[0] == 0:
                    service_monitor_result["service"][service_name]["status"] = "start"
                    info_handler(message = f"Service {service_name} has been restarted successfully.")
                else:
                    error_handler(message = f"Failed to restart service {service_name}.")
            except Exception as e:
                error_handler(message = f"Failed to execute command: {service_restart_command}. Error: {e}")
                continue

        if stop_service:
            send_alert_message(stop_service)


class SSHClusterServiceMonitor:
    def __init__(self, node_ip, services, service_monitor_result, ssh_client):
        self.services = services
        self.service_monitor_result = service_monitor_result
        self.node_services = get_services_by_ip(ip = node_ip)
        self.ssh_client = ssh_client
        self.node_ip = node_ip

    def ssh_node_services_monitor(self):
        """
        获取主机下所存在的所有服务状态
        """
        for service_name in self.services:
            debug_handler(message = f"Getting Host: {self.node_ip} {service_name} status...")

            if not self.ssh_get_service_status(service_name = service_name):
                # 需要启动服务
                self.service_monitor_result[
                    service_name
                ]["restart_count"] += 1
                start_service_command = service_operation_command(
                    service_name = service_name, operation_name = "start"
                )
                debug_handler(message = f"Executing command: {start_service_command}")
                if not start_service_command:
                    error_handler(
                        message = f"Failed to get start command for Host: {self.node_ip} service {service_name}."
                    )
                    self.service_monitor_result[
                        service_name
                    ] = "Failed to get start command for service"
                    continue
                start_service_output = execute_ssh_command(
                    ssh_client = self.ssh_client, command = start_service_command
                )
                debug_handler(message = f"Command output: {start_service_output}")
                if start_service_output[0] == 0 and self.ssh_get_service_status(service_name = service_name):
                    """
                    只有在启动成功并且状态正常时才更新状态
                    """
                    self.service_monitor_result[service_name]["status"] = "start"
                    info_handler(
                        message = f"Host: {self.node_ip} Service {service_name} has been started successfully."
                    )
                else:
                    error_handler(message = f"Failed to start service {service_name} on Host: {self.node_ip}.")
                    self.service_monitor_result[
                        service_name
                    ]["status"] = "stop"
            else:
                # 服务正常
                info_handler(message = f"Host: {self.node_ip} Service {service_name} is running.")
                self.service_monitor_result[service_name]["status"] = "start"

        return self.service_monitor_result

    def ssh_get_service_status(self, service_name):
        """
        获取指定服务状态
        """
        service_status_command = service_operation_command(
            service_name = service_name, operation_name = "status"
        )
        debug_handler(message = f"Executing command: {service_status_command}")
        service_status_output = execute_ssh_command(
            ssh_client = self.ssh_client, command = service_status_command
        )
        debug_handler(message = f"Command output: {service_status_output}")
        if service_status_output[1]:
            return True
        else:
            return False


class AgentClusterServiceMonitor:
    def __init__(self, node_ip, services, service_monitor_result, agent_client):
        self.services = services
        self.service_monitor_result = service_monitor_result
        self.node_services = get_services_by_ip(ip = node_ip)
        self.agent_client = agent_client
        self.node_ip = node_ip

    def agent_node_services_monitor(self):
        """
        获取主机下所存在的所有服务状态
        """
        for service_name in self.node_services:
            debug_handler(message = f"Getting {service_name} status...")
            if not self.agent_get_service_status(service_name = service_name):
                # 需要启动服务
                self.service_monitor_result[
                    service_name
                ]["restart_count"] += 1
                start_service_command = service_operation_command(
                    service_name = service_name, operation_name = "start"
                )
                debug_handler(message = f"Executing command: {start_service_command}")
                if not start_service_command:
                    error_handler(message = f"Failed to get start command for service {service_name}.")
                    self.service_monitor_result[
                        service_name
                    ] = "Failed to get start command for service"
                    continue
                start_service_output = self.agent_client.root.command(
                    command = start_service_command
                )
                debug_handler(message = f"Command output: {start_service_output}")
                if start_service_output[0] == 0 and self.agent_get_service_status(service_name = service_name):
                    """
                    只有在启动成功并且状态正常时才更新状态
                    """
                    self.service_monitor_result[service_name]["status"] = "start"
                    info_handler(
                        message = f"Host: {self.node_ip} Service {service_name} has been started successfully."
                    )
                else:
                    error_handler(message = f"Failed to start service {service_name} on Host: {self.node_ip}.")
                    self.service_monitor_result[
                        service_name
                    ]["status"] = "stop"
            else:
                # 服务正常
                info_handler(message = f"Host: {self.node_ip} Service {service_name} is running.")
                self.service_monitor_result[service_name]["status"] = "start"
        return self.service_monitor_result

    def agent_get_service_status(self, service_name):
        """
        获取指定服务状态
        :param service_name: 服务名称
        :return: 服务状态
        """
        service_status_command = service_operation_command(
            service_name = service_name, operation_name = "status"
        )
        debug_handler(message = f"Executing command: {service_status_command}")
        service_status_output = self.agent_client.root.command(
            command = service_status_command
        )
        debug_handler(message = f"Command output: {service_status_output}")
        if service_status_output[1] and service_status_output[0] == 0:
            return True
        else:
            return False


def process_node_services(node_ip, node_config, service_monitor_result):
    """
    Process the services of a node. | 处理节点服务
    :param node_ip:
    :param services:
    :return:
    """
    info_handler(message = f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Processing node {node_ip}")
    services = get_services_by_ip(node_ip)
    node_connection_type = node_config.get("deployment_type", "ssh")
    if node_connection_type == "ssh":
        from app.cluster.cluster_for_ssh.tools.node_connect import node_connect
        ssh_client, sftp_client, connect_message = node_connect(node_ip = node_ip)
        if not ssh_client:
            error_handler(message = f"Failed to connect to node {node_ip}. Error: {connect_message}")
            return "Failed to connect to node"
        ssh_service_monitor = SSHClusterServiceMonitor(
            node_ip = node_ip,
            services = services,
            service_monitor_result = service_monitor_result,
            ssh_client = ssh_client
        ).ssh_node_services_monitor()
        return ssh_service_monitor
    else:
        from app.cluster.cluster_for_agent.tools.node_connect import NodeAgentConnect
        agent_client, connect_message = NodeAgentConnect(node_ip = node_ip).get_node_conn()
        if not agent_client:
            error_handler(message = f"Failed to connect to node {node_ip}. Error: {connect_message}")
            return "Failed to connect to node"
        agent_service_monitor = AgentClusterServiceMonitor(
            node_ip = node_ip,
            services = services,
            service_monitor_result = service_monitor_result,
            agent_client = agent_client
        ).agent_node_services_monitor()
        return agent_service_monitor


def cluster_service_status():
    """
    Get the service status of a cluster node. | 获取集群节点服务状态
    :return:
    """
    # 启动一个进程池
    with multiprocessing.Pool(processes = multiprocessing.cpu_count()) as pool:
        process_node_with_services = partial(process_node_services)

        # 保存每个进程的 AsyncResult 对象
        async_results = {}

        # 并行处理每个节点
        for node_ip, node_config in deployment_config.cluster_nodes.items():
            async_result = pool.apply_async(
                process_node_with_services,
                args = (node_ip, node_config, service_monitor_result.get("nodes", {}).get(node_ip, {}))
            )
            async_results[node_ip] = async_result

        pool.close()
        pool.join()

        # 打印或处理结果
        for node_ip, result in async_results.items():
            if result.get():
                service_monitor_result["nodes"][node_ip] = result.get()
            else:
                debug_handler(message = f"Failed to get service status of node {node_ip}.")
                try:
                    service_monitor_result["nodes"].pop(node_ip, None)
                except Exception as e:
                    debug_handler(message = f"Failed to remove node {node_ip} from service_monitor_result. Error: {e}")
    try:
        with open(os.path.join(CURRENT_DIRECTORY, "service_monitor_dump.json"), "w") as f:
            json.dump(service_monitor_result, f, indent = 4)
    except Exception as e:
        error_handler(message = f"Failed to save service_monitor.json. Error: {e}")
        error_handler(message = traceback.format_exc())


def monitor_service():
    """
    Monitor the service. | 监控服务进程
    :return:
    """
    # 1. 判断集群类型
    if deployment_config.deployment_mode == "standalone":
        # 单机模式, 来修改
        # 获取服务状态
        StandaloneServiceStatus().service_monitor()
    else:
        if not deployment_config.cluster_nodes:
            error_handler(message = "No cluster nodes found in the deployment configuration file.")
            error_handler(message = "Please check the deployment configuration file.")
            sys.exit(1)
        for node_ip, _ in deployment_config.cluster_nodes.items():
            if node_ip not in service_monitor_result["nodes"]:
                service_monitor_result[
                    "nodes"
                ][node_ip] = {
                    service_name: {
                        "status": "",
                        "restart_count": 0
                    } for service_name in get_services_by_ip(node_ip)
                }
        # 集群模式
        cluster_service_status()


def main():
    """Main function. | 主函数"""
    setup_logger()

    service_monitor_params = argparse.ArgumentParser(description = "The standalone service monitor.")
    service_monitor_params.add_argument(
        "--port", type = int, default = 18080, help = "The port of the service."
    )
    service_monitor_params.add_argument(
        "--interval", type = int, default = 300, help = "The interval of the service monitor in seconds (minimum 180)."
    )
    service_monitor_group = service_monitor_params.add_mutually_exclusive_group()
    service_monitor_group.add_argument("--start", action = "store_true", help = "Start the service monitor.")
    service_monitor_group.add_argument("--stop", action = "store_true", help = "Stop the service monitor.")
    args = service_monitor_params.parse_args()

    # 设置环境变量
    os.environ[
        "MONITOR_INTERVAL"
    ] = str(args.interval)

    os.environ[
        "DEPLOYMENT_CONFIG_PATH"
    ] = os.path.join(
        CURRENT_DIRECTORY, "config", "deployment.yaml"
    )
    if args.stop:
        stop_service()
    elif args.start:
        monitor_service()
        start_service(args.port, args.interval)
    else:
        # 打印帮助信息
        service_monitor_params.print_help()


if __name__ == '__main__':
    main()
