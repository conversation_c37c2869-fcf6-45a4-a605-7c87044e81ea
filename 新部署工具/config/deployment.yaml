# other_files 用于放置其他的配置文件,如 hdfs-site.xml, core-site.xml等,会被拷贝到服务的conf目录下
root_user: true
# 设置每个服务的固定内存大小,如果服务器下的角色未设置内存大小,则参考此配置
roles_memory_size:
  api: 2.6
  master: 4
  worker: 8
  alert: 1
  whaletunnel: 8


# ==== 必填项 =====
# 部署模式
# 1. 单机 (standalone)
# 2. 集群 (cluster)
deployment_mode: Standalone

# 是否部署WhaleTunnel,默认为false
deploy_whaletunnel: false

# 部署目录(解压后文件存放)
deployment_dir: /data/whalestudio

# 安装包存储目录(本地)
package_dir: /data/whalestudio/package

# 服务日志保存目录
service_log_dir: /data/whalestudio/logs

# 用户数据本地存储路径, 默认为 /data/whalestudio/data_basedir
data_basedir_path: /data/whalestudio/data_basedir

# 数据源加密(默认true),如果为false,则不加密. 源数据中心的数据源配置
datasource_encrypted: true
# 租户功能开启(默认true),如果为false,则不启用租户功能
tenant_enable: true

# API Workflow 地址,如果有Nginx 代理,请配置Nginx地址,否则配置API Workflow地址
api_workflow_address: http://*************:12345
## 必须配置,如果不配置且部署了whaletunnel,会导致whaletunnel无法正常工作
## 如果集群部署时使用了agent模式,需要使用此地址提供安装包下载功能
local_ip: ***************

# 密码重置邮件发送配置, 默认可为空
reset_password_email:
  smtp_host: smtp.163.com
  smtp_port: 465
  smtp_username: <EMAIL>
  smtp_password: whalestudio

# 注册中心配置,支持ZooKeeper,MySQL
# Registration center configuration
registration_center:
  type: ZooKeeper
  host: ***************
  port: 2181
  timeout: 60
  # 如果ZooKeeper需要认证,请配置以下信息,或者删除以下信息
  namespace: whalestudio  # 仅ZooKeeper需要配置, 或者直接默认
  # 如果使用MySQL或PostgreSQL,请配置以下信息
  # username: root
  # password: root
  # database: registry

# 元数据库配置,支持MySQL,DM,PostgreSQL,Highgo,KingBase,OpenGauss
metabase:
  type: mysql
  host: ***************
  port: 3306
  username: root
  password: QWer12#$
  database: ldap_test
  # 如果需要分库分表,请配置以下信息
  sharding_enable: false
  # 如果需要配置 schema,请配置以下信息
  # schema: whalestudio
  # 如果需要大小写转换,请配置以下信息
  uppercase: true
  # 如果需要删除反引号,请配置以下信息
  remove_back_quote: true
  # 如果需要自定义jdbc url参数,请配置以下信息, 示例:
  # jdbc_url: "**************************************************************************************************"



# 环境变量配置路径,用于服务操作系统环境变量配置,默认为/etc/profile,多个路径用逗号分隔或换行分隔
system_environment_path: /etc/profile
# 多行示例:
# system_environment_path: /etc/profile,/etc/bashrc
#system_environment_path:
#  - /etc/profile
#  - /etc/bashrc

# 统一部署用户配置,用于所有服务统一使用同一个部署用户,如果不配置,则使用各个服务的部署用户
unified_deployment_user:
  username: root
  password: "qwe123"
  # 如果需要免密登录,请配置以下信息
  password_free: false
  public_key: /root/.ssh/id_rsa.pub
  # 如果需要配置端口,请配置以下信息
  port: 22
  # 如果需要配置超时时间,请配置以下信息
  timeout: 60

cluster_nodes:
  # 所有的部署主机
  # 格式: {主机IP: {主机配置}}
  # 主机配置:
  #   use_deployment_user: 是否使用统一部署用户, 默认 false
  #   username: 部署用户用户名
  #   password: 部署用户密码
  #   password_free: 是否免密登录, 默认 false
  #   public_key: 部署用户公钥
  #   server_memory_size: 服务器内存大小,单位GB,默认8G
  #   roles: 部署角色, 支持api, master, worker, alert, whaletunnel, all为所有角色
  #   roles_memory_size: 角色配置,不配置则使用全局配置
  #   service_log_dir: 日志路径, 默认为 /data/whalestudio/logs, 可选配置,如果不配置,则使用全局配置
  #   worker_pool_size: worker节点进程池大小, 默认为100, 仅在存在worker角色时生效
  #   network_interface: 注册网卡, 默认eth0, 可选配置,应用于服务器存在多网卡的情况
  # 示例:

  ***************:
    # 使用统一部署用户的配置示例
    # 是否使用统一部署用户, 默认 false
    use_deployment_user: true
    # 服务器内存大小,单位GB,默认16G
    # 部署角色, 支持api, master, worker, alert, whaletunnel, all为所有角色
    roles: all
    deployment_type: ssh  # 部署方式, 默认ssh, 可选配置,支持 agent, ssh
    # 主机下服务内存设置,如不设置则使用全局设置
    roles_memory_size:
      api: 2
      master: 4
      worker: 8
      alert: 1
      whaletunnel: 32
    # 日志路径, 默认为 /data/whalestudio/logs, 可选配置,如果不配置,则使用全局配置
    service_log_dir: /data/whalestudio/logs
    # worker节点进程池大小, 默认为100, 仅在存在worker角色时生效
    worker_pool_size: 100
    # 注册网卡, 默认eth0, 可选配置,应用于服务器存在多网卡的情况
    #network_interface: ens192
  192.168.124.157:
    # 不使用统一部署用户的配置示例
    # 是否使用统一部署用户, 默认 false
    use_deployment_user: false
    username: whalestudio
    password: QWer12#$
    password_free: false
    public_key: ""
    # 服务器内存大小,单位GB,默认16G
    # 部署角色, 支持api, master, worker, alert, whaletunnel, all为所有角色
    roles: all
    deployment_type: ssh  # 部署方式, 默认ssh, 可选配置,支持 agent, ssh
    # 主机下服务内存设置,如不设置则使用全局设置
    roles_memory_size:
      api: 2
      master: 4
      worker: 8
      alert: 1
      whaletunnel: 32
    # 日志路径, 默认为 /data/whalestudio/logs, 可选配置,如果不配置,则使用全局配置
    service_log_dir: /data/whalestudio/logs
    # worker节点进程池大小, 默认为100, 仅在存在worker角色时生效
    worker_pool_size: 100
    # 注册网卡, 默认eth0, 可选配置,应用于服务器存在多网卡的情况
    #network_interface: ens192
  192.168.124.158:
    # 使用Agent部署模式
    # 部署角色, 支持api, master, worker, alert, whaletunnel, all为所有角色
    roles: all
    deployment_type: ssh  # 部署方式, 默认ssh, 可选配置,支持 agent, ssh
    # 主机下服务内存设置,如不设置则使用全局设置
    roles_memory_size:
      api: 2
      master: 4
      worker: 8
      alert: 1
      whaletunnel: 32
    # 日志路径, 默认为 /data/whalestudio/logs, 可选配置,如果不配置,则使用全局配置
    service_log_dir: /data/whalestudio/logs
    # worker节点进程池大小, 默认为100, 仅在存在worker角色时生效
    worker_pool_size: 100
    # 注册网卡, 默认eth0, 可选配置,应用于服务器存在多网卡的情况
    #network_interface: ens192

# whalestudio_environment 中的内容优先级高于 environment.yaml 中的内容
# 如果需要配置环境变量,请配置以下内容
# 默认为空.
whalestudio_environment:
# 示例:
# 设置注册所用的网卡名称:
  # dolphin.scheduler.network.interface.preferred="eth0"



# 资源中心配置
# 资源中心配置,支持LocalFile , HDFS, S3, OSS

## 本地文件系统配置示例 ###
resource_center:
  type: LocalFile # 本地文件系统
  local_file_path: /data/whalestudio/upload # 本地文件系统路径



### HDFS 无 Kerberos 配置示例 ###
### 如果 hdfs 启用了 ha, 则需要在当前目录下创建 other_files 目录, 并将 core-site.xml 和 hdfs-site.xml 放入
#resource_center:
#  type: HDFS
#  hdfs_url: hdfs://***************:8020 # HDFS URL
#  hdfs_user: root # HDFS 用户名
#  hdfs_file_path: /data/whalestudio/upload  # HDFS 文件路径


### HDFS 带 Kerberos 配置示例 ###
#resource_center:
#  type: HDFS
#  hdfs_url: hdfs://***************:8020 # HDFS URL
#  hdfs_user: root # HDFS 用户名
#  hdfs_file_path: /data/whalestudio/upload  # HDFS 文件路径
#  kerberos_enabled: true # 是否启用 Kerberos
#  keytab_username: <EMAIL> # Kerberos 用户名
#  keytab_path: /data/whalestudio/keytab/hdfs.keytab # Kerberos keytab 文件路径
#  krb5_conf_path: /etc/krb5.conf # Kerberos 配置文件路径


### S3 配置示例 ###
#resource_center:
#  type: S3
#  no_authentication_required: false # 是否无需认证 (默认 false)
#  access_key_id: AKIAIOSFODNN7EXAMPLE # S3 访问密钥 ID
#  secret_access_key: wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY # S3 访问密钥
#  bucket_name: my-bucket # S3 桶名
#  endpoint: http://***************:9000 # S3 端点
#  region: us-east-1 # S3 区域


### OSS 配置示例 ###F
#resource_center:
#  type: OSS
#  access_key_id: AKIAIOSFODNN7EXAMPLE # OSS 访问密钥 ID
#  secret_access_key: wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY # OSS 访问密钥
#  region: oss-cn-hangzhou # OSS 区域
#  bucket_name: my-bucket # OSS 桶名
#  endpoint: http://oss-cn-hangzhou.aliyuncs.com # OSS 端点