################
#服务监视器配置
#此文件包含服务监视器的配置。
#
#服务监视器负责监视服务的状态
#在设备上运行。它定期检查每项服务的状态
#如果状态发生变化，则发送通知。


# 是否需要在服务状态发生变化时发送通知
send_notification: true

# 告警方式
# 可选值：email, 飞书, 企业微信, HTTP请求,
# 默认值：email
alert_method: wecom

# 标题
title: "服务状态变化通知"


email:
  # 邮件服务器地址
  smtp_server: smtp.gmail.com
  # 邮件服务器端口
  smtp_port: 587
  # 邮件服务器用户名
  smtp_username: your_email_address
  # 邮件服务器密码
  smtp_password: your_email_password
  # 邮件发送者
  sender_email: your_email_address
  # 邮件接收者
  receiver_email: your_email_address

feishu:
  # 飞书机器人webhook地址
  webhook: https://open.feishu.cn/open-apis/bot/v2/hook/11b7c2c4-0320-4153-afcd-b7d699d4e7be

wecom:
  # 企业微信机器人webhook地址
  webhook: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=29ec085d-45e3-4277-adf7-52ac1b429f0f
