#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : deploy.py
# @Time    : 2025/07/28 13:37
# <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import sys
import argparse
from argparse import ArgumentParser

from loguru import logger

CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


def setup_logging(level: str = "INFO"):
    """
    Setup logging for the application. | 为应用程序设置日志记录。
    """
    logger.remove()

    log_path = os.path.join(CURRENT_DIRECTORY, 'logs')
    if not os.path.exists(log_path):
        try:
            os.makedirs(log_path)
        except PermissionError:
            print(f"请检查当前用户是否有权限创建目录: {log_path}")
            return
        except FileExistsError:
            pass
        except Exception as e:
            print(f"创建日志目录失败: {e}")
            return

    try:
        logger.add(
            os.path.join(log_path, 'deploy.log'),
            rotation = '10 MB',
            retention = '10 days',
            level = level.upper(),
            format = '{time:YYYY-MM-DD HH:mm:ss} | {level} | {file} | {line} | {message}',
            encoding = 'utf-8'
        )
    except Exception as e:
        print(f"添加日志记录器失败: {e}")
        return


# 操作分发
def dispatch_operation(operation_type: str, operation_value: str, hosts: str = None):
    """
    Dispatch operation. | 分发操作。
    :param operation_type: Operation type. | 操作类型。
    :param operation_value: Operation value. | 操作值。
    :param hosts: Hosts. | 主机。
    :return:
    """
    # 1. 要导入config
    from app.config.config_generation import deployment_config
    from app.common.utils import get_setup_user
    from app.common.console_print import error_header, debug_header

    current_setup_user = get_setup_user()

    if current_setup_user == "root" and not deployment_config.root_user:
        error_header(message = "不允许使用Root用户进行脚本的启动.请使用普通用户进行操作.")
        return
    debug_header(message = f"当前用户: {current_setup_user}")
    if deployment_config.deployment_mode == "standalone":
        from app.standalone.main import standalone_main
        standalone_main(
            operation = operation_type,
            args = operation_value
        )
    elif deployment_config.deployment_mode == "cluster":
        print("集群部署")
    else:
        error_header(message = "部署模式不正确, 请选择 standalone 或 cluster。")
        return


class DeployMain:
    """
    DeployMain class. | 部署主类。
    """

    @classmethod
    def setting_env(cls):
        """
        Setting environment variables. | 设置环境变量。
        """
        from app.common.console_print import debug_header
        env_vars = {
            'WHALE_TUNNEL_CONFIG_PATH': os.path.join(CURRENT_DIRECTORY, "config", "seatunnel.yaml"),
            'DEPLOYMENT_CONFIG_PATH': os.path.join(CURRENT_DIRECTORY, "config", "deployment.yaml"),
            'ENVIRONMENT_CONFIG_PATH': os.path.join(CURRENT_DIRECTORY, "config", "environment.yaml"),
            'OTHER_FILES_PATH': os.path.join(CURRENT_DIRECTORY, "config", "other_files")
        }

        for key, value in env_vars.items():
            try:
                os.environ[key] = value
                debug_header(f"设置环境变量: {key}={value} 成功")
            except Exception as e:
                debug_header(f"设置环境变量: {key}={value} 失败: {e}")

    @classmethod
    def get_operation_from_args(cls, args: dict):
        """从参数中获取操作类型和值"""
        for key, value in args.items():
            if value is not False and value is not None:
                return key, value
        return None, None

    @classmethod
    def set_args(cls):
        """
        Set command line arguments. | 设置命令行参数。
        """
        parser = ArgumentParser(
            description = 'WhaleStudio 部署工具',
            formatter_class = argparse.RawDescriptionHelpFormatter,
            epilog = """
使用示例：
    %(prog)s --install  # 安装WhaleStudio
    %(prog)s --uninstall  # 卸载WhaleStudio
    %(prog)s --start  # 启动WhaleStudio所有的服务
    %(prog)s --stop  # 停止WhaleStudio所有的服务
    %(prog)s --restart  # 重启WhaleStudio所有的服务
    %(prog)s --status  # 查看WhaleStudio的运行状态
            """
        )

        parser.add_argument(
            "--log_level", default = "INFO",
            choices = ["DEBUG", "INFO", "WARNING", "ERROR"],
            help = "设置日志级别"
        )
        parser.add_argument(
            "--hosts", nargs = "+", default = "all",
            help = "指定操作的主机, 默认为 all, 也可以指定具体的主机, 多个主机用空格分隔"
        )
        parser_group = parser.add_mutually_exclusive_group(required = True)

        parser_group.add_argument("--install", action = "store_true", help = "安装WhaleStudio")
        parser_group.add_argument("--uninstall", action = "store_true", help = "卸载WhaleStudio")

        parser_group.add_argument(
            "--start", nargs = "?", const = "all",
            choices = ["all", "api", "master", "worker", "alert", "whaletunnel"],
            help = "启动WhaleStudio的服务"
        )
        parser_group.add_argument(
            "--stop", nargs = "?", const = "all",
            choices = ["all", "api", "master", "worker", "alert", "whaletunnel"],
            help = "停止WhaleStudio的服务"
        )
        parser_group.add_argument(
            "--restart", nargs = "?", const = "all",
            choices = ["all", "api", "master", "worker", "alert", "whaletunnel"],
            help = "重启WhaleStudio的服务"
        )
        parser_group.add_argument(
            "--status", nargs = "?", const = "all",
            choices = ["all", "api", "master", "worker", "alert", "whaletunnel"],
            help = "查看WhaleStudio的服务状态"
        )
        parser_group.add_argument(
            "--logs", nargs = "?", const = "api",
            choices = ["all", "api", "master", "worker", "alert", "whaletunnel"],
            help = "查看WhaleStudio的日志"
        )
        parser_group.add_argument("--config_update", action = "store_true", help = "更新WhaleStudio的配置")
        parser_group.add_argument(
            "--db_init", nargs = "?", const = "None",
            choices = ["None", "show"],
            help = "初始化数据库"
        )
        parser_group.add_argument(
            "--db_upgrade", nargs = "?", const = "None",
            choices = ["None", "show"],
            help = "升级数据库"
        )
        parser_group.add_argument("--clean_packages", action = "store_true", help = "清理WhaleStudio的安装包")
        parser_group.add_argument("--pre_check", action = "store_true", help = "检查系统环境是否满足部署要求")
        parser_group.add_argument("--rollback", action = "store_true", help = "回滚WhaleStudio的版本")

        return parser

    @classmethod
    def main(cls):
        """
        Main function. | 主函数。
        """
        parser = cls.set_args()
        args = parser.parse_args()
        setup_logging(level = args.log_level)
        logger.debug(f"命令行参数: {args}")

        deploy_args = {
            "install": args.install,
            "uninstall": args.uninstall,
            "start": args.start,
            "stop": args.stop,
            "restart": args.restart,
            "status": args.status,
            "logs": args.logs,
            "config_update": args.config_update,
            "db_init": args.db_init,
            "db_upgrade": args.db_upgrade,
            "clean_packages": args.clean_packages,
            "pre_check": args.pre_check,
            "rollback": args.rollback,
        }

        operation_type, operation_value = cls.get_operation_from_args(args = deploy_args)
        if not operation_type:
            logger.error("未指定操作类型, 请使用 --help 查看帮助")
            parser.print_help()
            return

        cls.setting_env()
        dispatch_operation(
            operation_type = operation_type,
            operation_value = operation_value,
            hosts = args.hosts,
        )


if __name__ == '__main__':
    DeployMain.main()
