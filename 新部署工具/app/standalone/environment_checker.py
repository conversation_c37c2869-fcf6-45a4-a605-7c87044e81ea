#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : environment_checker.py
# @Time    : 2025/08/01 16:00
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0
import os
import sys
import shutil
import platform
import subprocess
import psutil
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

from app.common.console_print import debug_header, info_header, warning_header, error_header, console, title_header
from app.config.config_generation import deployment_config


@dataclass
class CheckResult:
    """检查结果数据类"""
    name: str
    passed: bool
    message: str
    details: str = ""
    suggestion: str = ""


class EnvironmentChecker:
    """环境检查工具类"""

    # 最低系统要求
    MIN_REQUIREMENTS = {
        'memory_gb': 16,  # 最低内存要求 4GB
        'disk_gb': 30,  # 最低磁盘空间 20GB
    }

    def __init__(self):
        """初始化环境检查器"""
        self.results: List[CheckResult] = []
        self.system_info = self._get_system_info()

    def run_all_checks(self) -> Dict[str, any]:
        """
        运行所有环境检查
        :return: 检查结果汇总
        """

        title_header("开始环境检查", 100)
        with console.status("[bold green]正在检查环境...[/bold green]"):
            self.results.clear()
            # 执行各项检查
            self._check_system_info()
            self._check_memory()
            self._check_disk_space()
            self._check_permissions()
            self._check_java_environment()
            self._check_system_limits()

        return self._generate_summary()

    def _check_system_info(self):
        """检查系统基本信息"""
        try:
            system = platform.system()
            release = platform.release()
            machine = platform.machine()

            if system.lower() not in ['linux', 'unix']:
                self.results.append(
                    CheckResult(
                        name = "操作系统",
                        passed = False,
                        message = f"不支持的操作系统: {system}",
                        suggestion = "请使用 Linux 或 macOS 系统"
                    )
                )
            else:
                self.results.append(
                    CheckResult(
                        name = "操作系统",
                        passed = True,
                        message = f"{system} {release} ({machine})",
                        details = f"系统: {system}, 版本: {release}, 架构: {machine}"
                    )
                )
        except Exception as e:
            self.results.append(
                CheckResult(
                    name = "操作系统",
                    passed = False,
                    message = f"获取系统信息失败: {e}"
                )
            )

    def _check_memory(self):
        """检查内存"""
        if deployment_config.skip_memory_check:
            warning_header("跳过内存检查")
            return

        try:
            memory = psutil.virtual_memory()
            total_gb = memory.total / (1024 ** 3)
            available_gb = memory.available / (1024 ** 3)
            min_memory = self.MIN_REQUIREMENTS['memory_gb']

            if total_gb >= min_memory:
                self.results.append(
                    CheckResult(
                        name = "内存检查",
                        passed = True,
                        message = f"总内存: {total_gb:.1f}GB, 可用: {available_gb:.1f}GB",
                        details = f"内存使用率: {memory.percent}%"
                    )
                )
            else:
                self.results.append(
                    CheckResult(
                        name = "内存检查",
                        passed = False,
                        message = f"内存不足: {total_gb:.1f}GB",
                        suggestion = f"建议至少 {min_memory}GB 内存"
                    )
                )
        except Exception as e:
            self.results.append(
                CheckResult(
                    name = "内存检查",
                    passed = False,
                    message = f"检查内存失败: {e}"
                )
            )

    def _check_disk_space(self):
        """检查磁盘空间"""
        if deployment_config.skip_disk_space_check:
            warning_header("跳过磁盘空间检查")
            return

        try:
            # 检查部署目录所在磁盘
            deployment_dir = deployment_config.deployment_dir
            if not os.path.exists(deployment_dir):
                os.makedirs(deployment_dir, exist_ok = True)

            disk_usage = shutil.disk_usage(deployment_dir)
            free_gb = disk_usage.free / (1024 ** 3)
            total_gb = disk_usage.total / (1024 ** 3)
            min_disk = self.MIN_REQUIREMENTS['disk_gb']

            if free_gb >= min_disk:
                self.results.append(
                    CheckResult(
                        name = "磁盘空间",
                        passed = True,
                        message = f"可用空间: {free_gb:.1f}GB / {total_gb:.1f}GB",
                        details = f"部署目录: {deployment_dir}"
                    )
                )
            else:
                self.results.append(
                    CheckResult(
                        name = "磁盘空间",
                        passed = False,
                        message = f"磁盘空间不足: {free_gb:.1f}GB",
                        suggestion = f"建议至少 {min_disk}GB 可用空间"
                    )
                )
        except Exception as e:
            self.results.append(
                CheckResult(
                    name = "磁盘空间",
                    passed = False,
                    message = f"检查磁盘空间失败: {e}"
                )
            )

    def _check_permissions(self):
        """检查目录权限"""
        if deployment_config.skip_disk_permission_check:
            warning_header("跳过磁盘权限检查")
            return

        directories_to_check = [
            deployment_config.deployment_dir,
            deployment_config.package_dir,
            deployment_config.service_log_dir
        ]

        for directory in directories_to_check:
            try:
                # 创建目录（如果不存在）
                os.makedirs(directory, exist_ok = True)

                # 测试写权限
                test_file = os.path.join(directory, '.permission_test')
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)

                self.results.append(
                    CheckResult(
                        name = f"目录权限 ({os.path.basename(directory)})",
                        passed = True,
                        message = f"权限正常: {directory}"
                    )
                )
            except Exception as e:
                self.results.append(
                    CheckResult(
                        name = f"目录权限 ({os.path.basename(directory)})",
                        passed = False,
                        message = f"权限不足: {directory}",
                        suggestion = f"请确保对目录有读写权限: {e}"
                    )
                )

    def _check_java_environment(self):
        """检查Java环境"""
        try:
            result = subprocess.run(
                ['java', '-version'],
                capture_output = True, text = True, timeout = 10
            )
            if result.returncode == 0:
                java_version = result.stderr.split('\n')[0] if result.stderr else result.stdout.split('\n')[0]
                self.results.append(
                    CheckResult(
                        name = "Java环境",
                        passed = True,
                        message = f"Java已安装: {java_version.strip()}"
                    )
                )
            else:
                self.results.append(
                    CheckResult(
                        name = "Java环境",
                        passed = False,
                        message = "Java未正确安装",
                        suggestion = "请安装Java 8或更高版本"
                    )
                )
        except Exception as e:
            self.results.append(
                CheckResult(
                    name = "Java环境",
                    passed = False,
                    message = f"检查Java环境失败: {e}",
                    suggestion = "请安装Java 8或更高版本"
                )
            )

    def _check_system_limits(self):
        """检查系统限制"""
        try:
            # 检查文件描述符限制
            import resource
            soft_limit, hard_limit = resource.getrlimit(resource.RLIMIT_NOFILE)

            if soft_limit >= 65536:
                self.results.append(
                    CheckResult(
                        name = "文件描述符限制",
                        passed = True,
                        message = f"当前限制: {soft_limit}"
                    )
                )
            else:
                self.results.append(
                    CheckResult(
                        name = "文件描述符限制",
                        passed = False,
                        message = f"限制过低: {soft_limit}",
                        suggestion = "建议设置 ulimit -n 65536 或更高"
                    )
                )
        except Exception as e:
            self.results.append(
                CheckResult(
                    name = "文件描述符限制",
                    passed = False,
                    message = f"检查系统限制失败: {e}"
                )
            )

    def _get_system_info(self) -> Dict[str, str]:
        """获取系统信息"""
        try:
            return {
                'platform': platform.platform(),
                'system': platform.system(),
                'release': platform.release(),
                'version': platform.version(),
                'machine': platform.machine(),
                'processor': platform.processor(),
                'python_version': platform.python_version(),
                'hostname': platform.node()
            }
        except Exception:
            return {}

    def _generate_summary(self) -> Dict[str, any]:
        """生成检查结果汇总"""
        passed_count = sum(1 for result in self.results if result.passed)
        failed_count = len(self.results) - passed_count

        summary = {
            'total_checks': len(self.results),
            'passed': passed_count,
            'failed': failed_count,
            'success_rate': (passed_count / len(self.results) * 100) if self.results else 0,
            'results': self.results,
            'system_info': self.system_info
        }

        # 打印汇总信息
        if failed_count == 0:
            info_header(f"环境检查完成: 全部通过 ({passed_count}/{len(self.results)})")
        else:
            warning_header(f"环境检查完成: {failed_count} 项失败 ({passed_count}/{len(self.results)})")
        self.print_detailed_results()
        return summary

    def print_detailed_results(self):
        """打印详细的检查结果"""
        info_header("=== 详细检查结果 ===")

        for result in self.results:
            if result.passed:
                info_header(f"✓ {result.name}: {result.message}")
            else:
                error_header(f"✗ {result.name}: {result.message}")
                if result.suggestion:
                    warning_header(f"  建议: {result.suggestion}")

            if result.details:
                debug_header(f"  详情: {result.details}")
