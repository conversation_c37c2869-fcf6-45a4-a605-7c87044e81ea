#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : install.py
# @Time    : 2025/08/01 11:51
# <AUTHOR> chen<PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import sys

from app.standalone.environment_checker import EnvironmentChecker
from app.config.config_generation import deployment_config
from app.common.console_print import error_header

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class StandaloneInstaller:
    """
    StandaloneInstaller class. | 单机安装器类。
    """

    @staticmethod
    def install_package():
        if not deployment_config.skip_pre_check:
            if EnvironmentChecker().run_all_checks().get(
                    "success_rate"
            ) < 100:
                error_header(message = "请先检查环境是否满足安装要求。")
                sys.exit(1)

        pass
