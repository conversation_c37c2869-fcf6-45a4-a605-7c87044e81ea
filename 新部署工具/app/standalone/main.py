#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : main.py
# @Time    : 2025/08/01 11:52
# <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import sys

from app.standalone.install import StandaloneInstaller

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


def standalone_main(operation, args):
    """
    This function is called when the script is run as a standalone executable.
    这里是单机可执行脚本的入口函数
    :param operation: 操作类型
    :param args: 参数
    :return:
    """
    match operation:
        case 'install':
            StandaloneInstaller.install_package()
