#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : file_helper.py
# @Time    : 2025/07/30 11:56
# <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

import json
import os
import yaml
from typing import Tuple, Optional, Any

from app.common.console_print import error_header


class FileHelper:
    """文件操作助手类"""

    @staticmethod
    def get_file_name(file_path: str) -> str:
        """获取指定文件路径的文件名"""
        return os.path.basename(file_path)

    @staticmethod
    def read_file(file_path: str, file_type: str = "txt") -> Tuple[Optional[Any], Optional[str]]:
        """
        读取指定文件的内容
        :param file_path: 指定文件路径
        :param file_type: 文件类型 (txt, json, yaml)
        :return: (文件内容, 错误信息)
        """
        if not os.path.isfile(file_path):
            return None, "指定的文件不存在。"
        
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                if file_type == "json":
                    return json.load(f), None
                elif file_type == "yaml":
                    return yaml.safe_load(f), None
                else:
                    return f.readlines(), None
                    
        except PermissionError:
            return None, "没有权限读取文件。"
        except yaml.YAMLError as e:
            if hasattr(e, "problem_mark"):
                mark = e.problem_mark
                error_msg = f"读取{file_path}错误。错误位置：第{mark.line + 1}行，第{mark.column + 1}列。"
                error_header(error_msg)
                return None, error_msg
            return None, f"YAML解析错误: {str(e)}"
        except json.JSONDecodeError as e:
            error_msg = f"JSON解析错误: {str(e)}"
            error_header(error_msg)
            return None, error_msg
        except Exception as e:
            error_msg = f"读取文件失败: {str(e)}"
            error_header(error_msg)
            return None, error_msg