#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : package_helper.py
# @Time    : 2025/08/01 15:30
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys
import glob
import hashlib
import tarfile
import zipfile
from typing import List, Optional, Tuple, Dict
from datetime import datetime
from app.common.console_print import debug_header, info_header, warning_header, error_header, console
from app.config.config_generation import deployment_config


class PackageHelper:
    """安装包获取和管理工具类"""

    def get_latest_tarball(self) -> Optional[Dict[str, str]]:
        """
        获取最新的 .tar.gz 压缩包信息
        :return: 包信息字典 {name, path, md5, size, readable_size}
        """
        if not os.path.exists(deployment_config.package_dir):
            error_header(message = f"安装包目录不存在: {deployment_config.package_dir}")
            return None

        # 使用 glob 查找所有 .tar.gz 文件
        pattern = os.path.join(deployment_config.package_dir, "**", "*.tar.gz")
        tarball_files = glob.glob(pattern, recursive = True)

        if not tarball_files:
            error_header("未找到任何 .tar.gz 压缩包")
            return None

        # 按修改时间排序，获取最新的
        latest_file = max(tarball_files, key = os.path.getmtime)
        with console.status(f"[bold green]正在获取最新安装包信息...[/bold green]"):
            # 获取详细信息
            package_info = self._get_package_details(latest_file)
        if not self._test_archive(latest_file):
            error_header(f"最新安装包 {latest_file} 无法正常打开")
            error_header(message = "请检查安装包是否损坏或下载地址是否正确")
            sys.exit(1)
        return package_info

    def _get_package_details(self, file_path: str) -> Dict[str, str]:
        """
        获取包的详细信息
        :param file_path: 包文件路径
        :return: 包详细信息
        """
        file_stat = os.stat(file_path)
        file_size = file_stat.st_size
        file_name = os.path.basename(file_path)
        info_header(f"最新安装包: {file_name}")
        info_header(message = f"安装包下载时间: {datetime.fromtimestamp(os.path.getsize(file_path))}")
        return {
            'name': file_name,
            'path': file_path,
            'md5': self._calculate_md5(file_path),
            'size': str(file_size),
            'readable_size': self._format_size(file_size)
        }

    def _calculate_md5(self, file_path: str) -> str:
        """计算文件MD5校验和"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(8192), b""):
                    hash_md5.update(chunk)
            info_header(f"MD5校验和: {hash_md5.hexdigest()}")
            return hash_md5.hexdigest()
        except Exception as e:
            error_header(f"计算MD5失败: {e}")
            return ""

    def _format_size(self, size_bytes: int) -> str:
        """
        格式化文件大小为可读格式
        :param size_bytes: 字节大小
        :return: 可读的大小字符串
        """
        if size_bytes == 0:
            return "0B"

        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)

        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        formatted_size = "{:.2f}".format(size) + size_names[i]
        info_header(f"文件大小: {formatted_size}")
        return formatted_size

    def _test_archive(self, package_path: str) -> bool:
        """测试压缩包是否可以正常打开"""
        try:
            if package_path.endswith(('.tar.gz', '.tgz', '.tar')):
                with tarfile.open(package_path, 'r:*') as tar:
                    tar.getnames()  # 尝试读取文件列表
            elif package_path.endswith('.zip'):
                with zipfile.ZipFile(package_path, 'r') as zip_file:
                    zip_file.namelist()  # 尝试读取文件列表
            return True
        except Exception:
            return False
