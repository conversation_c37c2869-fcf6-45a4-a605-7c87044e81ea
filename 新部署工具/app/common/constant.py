#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : constant.py
# @Time    : 2025/07/28 13:46
# <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import sys

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))

"""
The following constants are used to define the paths of the files and directories used in the program. | 
以下常量用于定义程序中使用的文件和目录的路径。
"""

DEFAULT_ROLES_MEMORY_SIZE = {
    "api": 2,
    "master": 4,
    "worker": 8,
    "alert": 1,
    "whaletunnel": 8
}
