#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : path_helper.py
# @Time    : 2025/07/30 13:30
# <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import sys

CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


def get_parent_directory(path: str, level: int = 1) -> str:
    """
    获取指定路径的父目录
    :param path: 指定路径
    :param level: 父目录的层级
    :return: 父目录路径
    """
    for _ in range(level):
        path = os.path.dirname(path)
    return path
