#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : console_print.py
# @Time    : 2025/07/30 13:30
# <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

from loguru import logger
from rich.console import Console

console = Console()


def info_header(message):
    """Print an information header. | 打印信息头"""
    console.print(f"[bold green]{message}[/bold green]")
    logger.info(message)


def warning_header(message):
    """Print a warning header. | 打印警告头"""
    console.print(f"[bold yellow]{message}[/bold yellow]")
    logger.warning(message)


def error_header(message):
    """Print an error header. | 打印错误头"""
    console.print(f"[bold red]{message}[/bold red]")
    logger.error(message)


def debug_header(message):
    """Print a debug header. | 打印调试头"""
    logger.debug(message)
    if logger._core.min_level <= 10:
        console.print(f"[bold blue]{message}[/bold blue]")


def title_header(message, width = 100):
    """
    Print a title header. | 打印标题头,
    :param message: 标题内容
    :param width: 标题宽度
    :return:
    """
    console.print(f"[bold light_sky_blue1]{message.center(width, '=')}[/bold light_sky_blue1]")
