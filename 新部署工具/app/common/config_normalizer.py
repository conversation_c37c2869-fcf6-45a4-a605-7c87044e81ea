#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : config_normalizer.py
# @Time    : 2025/08/01 10:00
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

from typing import Dict, Any


class ConfigNormalizer:
    """配置文件大小写标准化工具"""
    
    # 标准字段名映射
    FIELD_MAPPINGS = {
        'deployment_mode': ['deployment_mode', 'deploymentmode', 'deploy_mode'],
        'deploy_whaletunnel': ['deploy_whaletunnel', 'deploywhaletunnel', 'whaletunnel'],
        'deployment_dir': ['deployment_dir', 'deploymentdir', 'deploy_dir'],
        'package_dir': ['package_dir', 'packagedir'],
        'service_log_dir': ['service_log_dir', 'servicelogdir', 'log_dir'],
        'data_basedir_path': ['data_basedir_path', 'databasedirpath', 'data_dir'],
        'datasource_encrypted': ['datasource_encrypted', 'datasourceencrypted'],
        'tenant_enable': ['tenant_enable', 'tenantenable'],
        'api_workflow_address': ['api_workflow_address', 'apiworkflowaddress'],
        'local_ip': ['local_ip', 'localip'],
        'registration_center': ['registration_center', 'registrationcenter'],
        'metabase': ['metabase', 'meta_base'],
        'resource_center': ['resource_center', 'resourcecenter'],
        'type': ['type'],
        'host': ['host'],
        'port': ['port'],
        'username': ['username'],
        'password': ['password'],
        'database': ['database'],
    }
    
    @classmethod
    def normalize_key(cls, key: str) -> str:
        """标准化配置键名"""
        key_lower = key.lower().replace('-', '_').replace(' ', '_')
        
        for standard_key, variants in cls.FIELD_MAPPINGS.items():
            if key_lower in [v.lower() for v in variants]:
                return standard_key
        
        return key_lower
    
    @classmethod
    def normalize_value(cls, key: str, value: Any) -> Any:
        """标准化配置值"""
        if isinstance(value, str):
            value_lower = value.lower().strip()
            
            if key in ['deployment_mode']:
                if value_lower in ['standalone', 'single']:
                    return 'standalone'
                elif value_lower in ['cluster', 'multi']:
                    return 'cluster'
            
            elif key in ['type'] and isinstance(value, str):
                # 数据库类型标准化
                if value_lower in ['mysql']:
                    return 'mysql'
                elif value_lower in ['postgresql', 'postgres']:
                    return 'postgresql'
                elif value_lower in ['dm', 'dameng']:
                    return 'dm'
                elif value_lower in ['highgo']:
                    return 'highgo'
                elif value_lower in ['kingbase', 'kingbase8']:
                    return 'kingbase'
                elif value_lower in ['opengauss']:
                    return 'opengauss'
                elif value_lower in ['oceanbase']:
                    return 'oceanbase'
                # 注册中心类型标准化
                elif value_lower in ['zookeeper', 'zk']:
                    return 'ZooKeeper'
                # 资源中心类型标准化
                elif value_lower in ['localfile', 'local']:
                    return 'LocalFile'
                elif value_lower in ['hdfs']:
                    return 'HDFS'
                elif value_lower in ['s3']:
                    return 'S3'
                elif value_lower in ['oss']:
                    return 'OSS'
            
            elif key in ['datasource_encrypted', 'tenant_enable', 'deploy_whaletunnel']:
                if value_lower in ['true', 'yes', '1', 'on', 'enable', 'enabled']:
                    return True
                elif value_lower in ['false', 'no', '0', 'off', 'disable', 'disabled']:
                    return False
        
        return value
    
    @classmethod
    def normalize_dict(cls, config_dict: Dict[str, Any]) -> Dict[str, Any]:
        """递归标准化配置字典"""
        if not isinstance(config_dict, dict):
            return config_dict
        
        normalized = {}
        for key, value in config_dict.items():
            normalized_key = cls.normalize_key(key)
            
            if isinstance(value, dict):
                normalized_value = cls.normalize_dict(value)
            elif isinstance(value, list):
                normalized_value = [
                    cls.normalize_dict(item) if isinstance(item, dict) else item
                    for item in value
                ]
            else:
                normalized_value = cls.normalize_value(normalized_key, value)
            
            normalized[normalized_key] = normalized_value
        
        return normalized
    
    @classmethod
    def normalize_config(cls, config: Dict[str, Any]) -> Dict[str, Any]:
        """标准化整个配置"""
        return cls.normalize_dict(config)