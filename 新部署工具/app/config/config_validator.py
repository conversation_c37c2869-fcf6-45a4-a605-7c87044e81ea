#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : config_validator.py
# @Time    : 2025/07/30 11:54
# <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

from typing import Optional, Dict, List


class ConfigValidator:
    """配置文件内容验证器"""
    
    def __init__(self):
        self.errors: List[str] = []
        self.warnings: List[str] = []
    
    def add_error(self, message: str):
        """添加错误信息"""
        self.errors.append(message)
    
    def add_warning(self, message: str):
        """添加警告信息"""
        self.warnings.append(message)
    
    def validate_deployment_config(self, config: Dict) -> bool:
        """验证部署配置"""
        self._check_required_fields(config)
        self._check_registration_center(config.get('registration_center'))
        self._check_metabase(config.get('metabase'))
        self._check_resource_center(config.get('resource_center'))
        
        return len(self.errors) == 0
    
    def _check_required_fields(self, config: Dict):
        """检查必需字段"""
        required_fields = [
            'deployment_mode', 'deployment_dir', 'package_dir',
            'registration_center', 'metabase', 'resource_center'
        ]
        
        for field in required_fields:
            if field not in config:
                self.add_error(f"缺少必需配置: {field}")
    
    def _check_registration_center(self, reg_config: Optional[Dict]):
        """检查注册中心配置"""
        if not reg_config:
            self.add_error("缺少必需配置: registration_center")
            return
        
        if 'type' not in reg_config:
            self.add_error("registration_center 缺少 type 字段")
        elif reg_config['type'] not in ['ZooKeeper', 'mysql', 'postgresql']:
            self.add_error(f"registration_center.type 值无效: {reg_config['type']}")
        
        required_fields = ['host', 'port']
        for field in required_fields:
            if field not in reg_config:
                self.add_error(f"registration_center 缺少 {field} 字段")
    
    def _check_metabase(self, meta_config: Optional[Dict]):
        """检查元数据库配置"""
        if not meta_config:
            self.add_error("缺少必需配置: metabase")
            return
        
        required_fields = ['type', 'host', 'port', 'username', 'password', 'database']
        for field in required_fields:
            if field not in meta_config:
                self.add_error(f"metabase 缺少 {field} 字段")
    
    def _check_resource_center(self, resource_config: Optional[Dict]):
        """检查资源中心配置"""
        if not resource_config:
            self.add_error("缺少必需配置: resource_center")
            return
        
        if 'type' not in resource_config:
            self.add_error("resource_center 缺少 type 字段")
            return
        
        resource_type = resource_config['type']
        
        if resource_type == 'LocalFile':
            if 'local_file_path' not in resource_config:
                self.add_error("LocalFile 类型的 resource_center 缺少 local_file_path 字段")
        elif resource_type == 'HDFS':
            required_fields = ['hdfs_url', 'hdfs_user', 'hdfs_file_path']
            for field in required_fields:
                if field not in resource_config:
                    self.add_error(f"HDFS 类型的 resource_center 缺少 {field} 字段")
        else:
            self.add_error(f"resource_center.type 值无效: {resource_type}")
