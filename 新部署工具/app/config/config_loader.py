#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : config_loader.py
# @Time    : 2025/07/30 11:53
# <AUTHOR> chenyi<PERSON><PERSON>
# @Version : 1.0

import os
import sys

from app.common.file_helper import FileHelper
from app.common.path_helper import get_parent_directory
from app.common.console_print import error_header, debug_header
from app.common.config_normalizer import ConfigNormalizer

CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))


class ConfigLoader:
    """配置加载器"""

    DEFAULT_DEPLOYMENT_CONFIG_FILE = os.getenv(
        "DEPLOYMENT_CONFIG_PATH",
        os.path.join(get_parent_directory(CURRENT_DIRECTORY, 2), "config", "deployment.yaml")
    )
    DEFAULT_WHALE_TUNNEL_CONFIG_FILE = os.getenv(
        "WHALE_TUNNEL_CONFIG_PATH",
        os.path.join(get_parent_directory(CURRENT_DIRECTORY, 2), "config", "seatunnel.yaml")
    )
    DEFAULT_ENVIRONMENT_CONFIG_FILE = os.getenv(
        "ENVIRONMENT_CONFIG_PATH",
        os.path.join(get_parent_directory(CURRENT_DIRECTORY, 2), "config", "environment.yaml")
    )

    @classmethod
    def load_deployment_config(cls) -> dict:
        """加载部署配置"""
        if not os.path.isfile(cls.DEFAULT_DEPLOYMENT_CONFIG_FILE):
            error_header(message = "部署配置文件不存在。请检查配置文件路径是否正确。")
            error_header(message = f"部署配置文件路径：{cls.DEFAULT_DEPLOYMENT_CONFIG_FILE}")
            sys.exit(1)

        connection_config, reload_msg = FileHelper.read_file(
            file_path = cls.DEFAULT_DEPLOYMENT_CONFIG_FILE,
            file_type = "yaml"
        )
        if not connection_config:
            error_header(message = reload_msg)
            sys.exit(1)

        normalized_config = ConfigNormalizer.normalize_config(connection_config)
        debug_header(message = "配置文件已标准化处理（大小写不敏感）")

        return normalized_config

    @classmethod
    def load_whale_tunnel_config(cls) -> dict or None:
        """加载 whale tunnel 配置"""
        if not os.path.isfile(cls.DEFAULT_WHALE_TUNNEL_CONFIG_FILE):
            debug_header(message = "没有默认的 whale tunnel 配置文件。")
            return None

        connection_config, _ = FileHelper.read_file(
            file_path = cls.DEFAULT_WHALE_TUNNEL_CONFIG_FILE,
            file_type = "yaml"
        )

        if connection_config:
            connection_config = ConfigNormalizer.normalize_config(connection_config)

        return connection_config

    @classmethod
    def load_environment_config(cls) -> dict or None:
        """加载环境配置"""
        if not os.path.isfile(cls.DEFAULT_ENVIRONMENT_CONFIG_FILE):
            debug_header(message = "没有默认的环境配置文件。")
            return None

        connection_config, _ = FileHelper.read_file(
            file_path = cls.DEFAULT_ENVIRONMENT_CONFIG_FILE,
            file_type = "yaml"
        )

        if connection_config:
            connection_config = ConfigNormalizer.normalize_config(connection_config)

        return connection_config
