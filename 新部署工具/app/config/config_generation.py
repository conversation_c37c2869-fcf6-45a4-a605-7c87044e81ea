#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : config_generation.py
# @Time    : 2025/07/30 11:55
# <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import sys
import traceback
from functools import lru_cache

from app.common.console_print import debug_header, info_header, warning_header, error_header
from app.config.config_loader import ConfigLoader
from app.models.deployment import Deployment

"""Get the current directory of the script. | 获取脚本当前目录"""
CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))

"""
config_generation.py is the script that generates the config.py file. | config_generation.py 是生成 config.py 文件的脚本。

The config.py file is used to store the configuration information of the program. | config.py 文件用于存储程序的配置信息。
"""


@lru_cache(maxsize = 1)
def deploy_config():
    """
    Get the deploy configuration. | 获取部署配置。
    :return:
    """
    deploy_config_content = ConfigLoader.load_deployment_config()
    try:
        return Deployment(**deploy_config_content)
    except Exception as e:
        error_header(f"加载部署配置失败：{e}")
        debug_header(message = traceback.format_exc())
        sys.exit(1)


deployment_config = deploy_config()
