#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File    : deployment.py
# @Time    : 2025/08/01 09:26
# <AUTHOR> chen<PERSON><PERSON><PERSON>
# @Version : 1.0

import os
import sys
from enum import Enum
from dataclasses import dataclass, field
from typing import List

from app.common.constant import DEFAULT_ROLES_MEMORY_SIZE
from app.common.console_print import error_header, debug_header, warning_header


class DeploymentMode(str, Enum):
    """
    Deployment mode. | 部署模式
    """
    STANDALONE = "standalone"
    CLUSTER = "cluster"


@dataclass
class Deployment:
    """
    Deployment class. | 部署类
    """
    root_user: bool = False
    skip_pre_check: bool = False
    skip_disk_permission_check: bool = False
    skip_disk_space_check: bool = False
    skip_sudo_check: bool = False
    skip_memory_check: bool = False
    roles_memory_size: dict = field(default_factory=lambda: DEFAULT_ROLES_MEMORY_SIZE.copy())
    deployment_mode: str = DeploymentMode.STANDALONE
    deploy_whaletunnel: bool = False
    deployment_dir: str = None
    package_dir: str = None
    service_log_dir: str = None
    data_basedir_path: str = None
    datasource_encrypted: bool = True
    tenant_enable: bool = True
    api_workflow_address: str = None
    local_ip: str = None
    reset_password_email: dict = field(default_factory=dict)
    registration_center: dict = field(default_factory=dict)
    metabase: dict = field(default_factory=dict)
    system_environment_path: str or List[str] = None
    whalestudio_environment: dict = field(default_factory=dict)
    resource_center: dict = field(default_factory=dict)
    cluster_nodes: dict[str, dict] = None
    unified_deployment_user: dict = None

    def __post_init__(self):
        """
        Post init. | 初始化后处理
        """
        if self.deployment_mode.upper() not in DeploymentMode.__members__:
            error_header(message="部署模式不正确, 请选择 standalone 或 cluster。")
            sys.exit(1)

        if not self.registration_center:
            error_header(message="注册中心配置不能为空。")
            sys.exit(1)

        if not self.metabase:
            error_header(message="元数据库配置不能为空。")
            sys.exit(1)

        if self.deployment_dir is None:
            self.deployment_dir = "/data/whalestudio"
            debug_header(message=f"部署目录未设置，使用默认值 {self.deployment_dir}。")

        if self.package_dir is None:
            error_header(message="请设置安装包存储目录。并上传安装包到该目录。")
            sys.exit(1)

        if self.service_log_dir is None:
            self.service_log_dir = os.path.join(self.deployment_dir, "logs")
            debug_header(message=f"服务日志目录未设置，使用默认值 {self.service_log_dir}。")

        for default_role_name, default_role_memory_size in DEFAULT_ROLES_MEMORY_SIZE.items():
            if default_role_name not in self.roles_memory_size:
                debug_header(
                    message=f"角色 {default_role_name} 的内存大小未设置，使用默认值 {default_role_memory_size} GB。"
                )
                self.roles_memory_size[default_role_name] = default_role_memory_size
        
        for role_name, role_memory_size in self.roles_memory_size.items():
            if not isinstance(role_memory_size, (int, float)):
                error_header(message=f"角色 {role_name} 的内存大小必须为数字。")
                sys.exit(1)
            if role_memory_size < 1:
                error_header(message=f"角色 {role_name} 的内存大小不能小于 1GB。")
                sys.exit(1)
            if role_memory_size > 128:
                warning_header(message=f"角色 {role_name} 的内存大小超过 128GB，可能导致系统性能问题。")
