pipeline {
    agent any
    environment {
        branch = '2.6-release'
        buildStatus = 'SUCCESS'
        jenkinsUrl = "http://**************/blue/organizations/jenkins/whalestudio-2.6-release/detail/whalestudio-2.6-release/${env.BUILD_NUMBER}/pipeline"
        gitUrl = '**************:WhaleOps'
        pythonScript = "/data/jenkins/whaleStudio_build.py"
    }
    stages {
        stage('Clean Workspace') {
            steps {
                echo "Cleaning workspace"
                cleanWs()
                echo "Workspace cleaned"
            }
        }
        stage('Send notification message') {
            steps {
                script {
                    message_send_command = "python ${env.pythonScript} --operation send_confirmation_message --jenkins_url ${env.jenkinsUrl} --branch ${env.branch}"
                    if (env.customer) {
                        message_send_command += " --customer ${env.customer}"
                    }
                    sh message_send_command
                }
            }
        }
        stage('user input') {
            steps {
                script {
                    def timeoutTime = 10 // minutes
                    try {
                        def userInput = timeout(time: timeoutTime, unit: 'MINUTES') {
                            input(
                                id: "userInput",
                                message: "请选择版本号客户版本等信息",
                                parameters: [
                                    choice(name: "customer", choices: ['None', '-P<PERSON><PERSON><PERSON>jiantou', '-Pguangdalicai', '-Pthird-party-IT', '-Prenshou', '-Prenbao'], description: "请选择客户名称"),
                                    choice(name: "update_to_OSS", choices: ['Yes', 'No'], description: "是否更新到OSS, Yes: 更新, No: 不更新，默认Yes"),
                                    string(name: "baseline_version", defaultValue: "${branch}", description: "请输入代码分支名，默认${branch}")
                                ]
                            )
                        }
                        echo "userInput: ${userInput}"
                        env.customer = userInput.customer
                        env.update_to_OSS = userInput.update_to_OSS
                        env.baseline_version = userInput.baseline_version
                        echo "customer: ${env.customer}"
                        echo "update_to_OSS: ${env.update_to_OSS}"
                        echo "baseline_version: ${env.baseline_version}"
                    } catch (org.jenkinsci.plugins.workflow.steps.FlowInterruptedException e) {
                        echo "用户输入超时，需要发送告警"
                        message_send_command = "python ${env.pythonScript} --operation send_message --jenkins_url ${env.jenkinsUrl} --build_status timeout --branch ${env.branch}"
                        if (env.customer) {
                            message_send_command += " --customer ${env.customer}"
                        }
                        sh message_send_command
                        error "用户输入超时"

                    } catch (Exception e) {
                        echo "用户输入异常，需要发送告警"
                        message_send_command = "python ${env.pythonScript} --operation send_message --jenkins_url ${env.jenkinsUrl} --build_status error --branch ${env.branch}"
                        if (env.customer) {
                            message_send_command += " --customer ${env.customer}"
                        }
                        sh message_send_command
                        error "用户输入异常"
                    }
                }
            }
        }

        stage('Get Latest Version') {
            steps {
                script {
                    echo "获取最新小版本号"
                    def command = "python ${env.pythonScript} --operation get_version --branch ${env.baseline_version}"
                    def get_version_command = (env.customer == "None" ? command : "${command} --customer ${env.customer}")
                    env.minor_version = sh(script: get_version_command, returnStdout: true).trim()
                    echo "最新小版本号: ${env.minor_version}"
                }
            }
        }

        stage('Send user selection notification') {
            steps {
                script {
                    message_send_command = "python ${env.pythonScript} --operation send_user_selection --jenkins_url ${env.jenkinsUrl} --branch ${env.baseline_version} --latest_version ${env.minor_version}"
                    if (env.customer && env.customer != 'None') {
                        message_send_command += " --customer ${env.customer}"
                    }
                    if (env.update_to_OSS) {
                        message_send_command += " --update_to_OSS ${env.update_to_OSS}"
                    }
                    sh message_send_command
                }
            }
        }

        stage('makedirs') {
            steps {
                script {
                    sh "mkdir ${WORKSPACE}/{whalescheduler,whaletunnel,whaletunnel-web}"
                    sh "mkdir ${WORKSPACE}/whalescheduler/whalescheduler-ui"
                }
            }
        }

        stage('clone whalescheduler') {
            steps {
                retry(3) {
                    echo "Cloning code from ${env.gitUrl}"
                    // 需要指定存储目录
                    dir("whalescheduler") {
                        git branch: "${env.baseline_version}", url: "${env.gitUrl}/whalescheduler.git"
                    }
                    echo "Code cloned"
                }
            }
        }
        stage('clone whalescheduler ui') {
            steps {
                retry(3) {
                    echo "Cloning code from ${env.gitUrl}"
                    dir("whalescheduler/whalescheduler-ui") {
                        git branch: "${env.baseline_version}", url: "${env.gitUrl}/whalescheduler-ui.git"
                    }
                    echo "Code cloned"
                }
            }
        }
        stage('clone whaletunnel') {
            steps {
                retry(3) {
                    echo "Cloning code from ${env.gitUrl}"
                    dir("whaletunnel") {
                        git branch: "${env.baseline_version}", url: "${env.gitUrl}/whaletunnel.git"
                    }
                    echo "Code cloned"
                }
            }
        }

        stage('clone whaletunnel web') {
            steps {
                retry(3) {
                    echo "Cloning code from ${env.gitUrl}"
                    dir("whaletunnel-web") {
                        git branch: "${env.baseline_version}", url: "${env.gitUrl}/whaletunnel-web.git"
                    }
                    echo "Code cloned"
                }
            }
        }
    }
}