pipeline {
    agent any
    
    options {
        timestamps()
        timeout(time: 2, unit: 'HOURS')
        buildDiscarder(logRotator(numToKeepStr: '10'))
        skipDefaultCheckout()
    }
    
    environment {
        // 基础配置
        BRANCH = '2.6-release'
        BUILD_STATUS = 'SUCCESS'
        JENKINS_URL = "http://***********:8080/blue/organizations/jenkins/whalestudio-2.6-release/detail/whalestudio-2.6-release/${env.BUILD_ID}/pipeline"
        
        // Git配置 - 使用HTTPS避免SSH问题
        GIT_TOKEN = '****************************************'
        GITHUB_BASE_URL = "https://${GIT_TOKEN}@github.com/WhaleOps"
        
        // Python脚本配置
        PYTHON_PATH = '/data/miniconda3/bin/python'
        BUILD_SCRIPT = '/data/jenkins/whaleStudio_build_test.py'
        
        // 项目目录配置
        WHALESCHEDULER_DIR = 'whalescheduler'
        WHALETUNNEL_DIR = 'whaletunnel'
        WHALETUNNEL_WEB_DIR = 'whaletunnel-web'
        WHALESCHEDULER_UI_DIR = 'whalescheduler/whalescheduler-ui'
        
        // 构建配置
        MAVEN_OPTS = '-Xmx4g -Xms2g'
        JAVA_TOOL_OPTIONS = '-Dfile.encoding=UTF-8'
    }
    
    stages {
        stage('Clean Workspace') {
            steps {
                echo "🧹 清理工作空间..."
                cleanWs()
                echo "✅ 工作空间清理完成"
            }
        }
        
        stage('User Input') {
            steps {
                script {
                    def timeoutTime = 10 // minutes
                    
                    try {
                        echo "⏳ 等待用户输入参数..."
                        
                        def userInput = timeout(time: timeoutTime, unit: 'MINUTES') {
                            input(
                                id: "userInput",
                                message: "请选择构建参数",
                                parameters: [
                                    choice(
                                        name: "customer", 
                                        choices: ['None', '-Pzhongxinjiantou', '-Pguangdalicai', '-Pthird-party-IT', '-Prenshou', '-Prenbao'], 
                                        description: "选择客户版本"
                                    ),
                                    choice(
                                        name: "update_to_OSS", 
                                        choices: ['Yes', 'No'], 
                                        description: "是否上传到OSS (默认: Yes)"
                                    ),
                                    string(
                                        name: "baseline_version", 
                                        defaultValue: "${BRANCH}", 
                                        description: "代码分支 (默认: ${BRANCH})"
                                    )
                                ]
                            )
                        }
                        
                        // 设置环境变量
                        env.CUSTOMER = userInput.customer
                        env.UPDATE_TO_OSS = userInput.update_to_OSS
                        env.BASELINE_VERSION = userInput.baseline_version
                        
                        echo "✅ 用户输入完成:"
                        echo "   客户版本: ${env.CUSTOMER}"
                        echo "   上传OSS: ${env.UPDATE_TO_OSS}"
                        echo "   代码分支: ${env.BASELINE_VERSION}"
                        
                        // 发送确认消息
                        sh """
                            ${PYTHON_PATH} ${BUILD_SCRIPT} \
                                --operation send_confirmation_message \
                                --jenkins_url "${JENKINS_URL}" \
                                --branch "${env.BASELINE_VERSION}" \
                                ${env.CUSTOMER != 'None' ? '--customer ' + env.CUSTOMER : ''}
                        """
                        
                    } catch (org.jenkinsci.plugins.workflow.steps.FlowInterruptedException e) {
                        echo "⏰ 用户输入超时"
                        
                        sh """
                            ${PYTHON_PATH} ${BUILD_SCRIPT} \
                                --operation send_message \
                                --jenkins_url "${JENKINS_URL}" \
                                --build_status timeout \
                                --branch "${BRANCH}" \
                                ${env.CUSTOMER ? '--customer ' + env.CUSTOMER : ''}
                        """
                        
                        error "用户输入超时，构建终止"
                        
                    } catch (Exception e) {
                        echo "❌ 用户输入异常: ${e.getMessage()}"
                        
                        sh """
                            ${PYTHON_PATH} ${BUILD_SCRIPT} \
                                --operation send_message \
                                --jenkins_url "${JENKINS_URL}" \
                                --build_status error \
                                --branch "${BRANCH}" \
                                ${env.CUSTOMER ? '--customer ' + env.CUSTOMER : ''}
                        """
                        
                        error "用户输入异常，构建终止"
                    }
                }
            }
        }
        
        stage('Setup Build Environment') {
            steps {
                script {
                    echo "🔧 设置构建环境..."
                    
                    // 创建项目目录
                    sh """
                        mkdir -p ${WORKSPACE}/${WHALESCHEDULER_DIR}
                        mkdir -p ${WORKSPACE}/${WHALETUNNEL_DIR}
                        mkdir -p ${WORKSPACE}/${WHALETUNNEL_WEB_DIR}
                        mkdir -p ${WORKSPACE}/${WHALESCHEDULER_UI_DIR}
                    """
                    
                    echo "✅ 构建环境设置完成"
                }
            }
        }
        
        stage('Clone Repositories') {
            parallel {
                stage('Clone WhaleScheduler') {
                    steps {
                        script {
                            retry(3) {
                                echo "📥 克隆 WhaleScheduler..."
                                dir("${WHALESCHEDULER_DIR}") {
                                    git branch: "${env.BASELINE_VERSION}", 
                                        url: "${GITHUB_BASE_URL}/whalescheduler.git",
                                        credentialsId: 'github-token'
                                }
                                echo "✅ WhaleScheduler 克隆完成"
                            }
                        }
                    }
                }
                
                stage('Clone WhaleScheduler UI') {
                    steps {
                        script {
                            retry(3) {
                                echo "📥 克隆 WhaleScheduler UI..."
                                dir("${WHALESCHEDULER_UI_DIR}") {
                                    git branch: "${env.BASELINE_VERSION}", 
                                        url: "${GITHUB_BASE_URL}/whalescheduler-ui.git",
                                        credentialsId: 'github-token'
                                }
                                echo "✅ WhaleScheduler UI 克隆完成"
                            }
                        }
                    }
                }
                
                stage('Clone WhaleTunnel') {
                    steps {
                        script {
                            retry(3) {
                                echo "📥 克隆 WhaleTunnel..."
                                dir("${WHALETUNNEL_DIR}") {
                                    git branch: "${env.BASELINE_VERSION}", 
                                        url: "${GITHUB_BASE_URL}/whaletunnel.git",
                                        credentialsId: 'github-token'
                                }
                                echo "✅ WhaleTunnel 克隆完成"
                            }
                        }
                    }
                }
                
                stage('Clone WhaleTunnel Web') {
                    steps {
                        script {
                            retry(3) {
                                echo "📥 克隆 WhaleTunnel Web..."
                                dir("${WHALETUNNEL_WEB_DIR}") {
                                    git branch: "${env.BASELINE_VERSION}", 
                                        url: "${GITHUB_BASE_URL}/whaletunnel-web.git",
                                        credentialsId: 'github-token'
                                }
                                echo "✅ WhaleTunnel Web 克隆完成"
                            }
                        }
                    }
                }
            }
        }
        
        stage('Get Version and Commit IDs') {
            steps {
                script {
                    echo "🔍 获取版本号和提交ID..."
                    
                    // 获取版本号
                    def versionOutput = sh(
                        script: """
                            ${PYTHON_PATH} ${BUILD_SCRIPT} \
                                --operation get_version \
                                --branch "${env.BASELINE_VERSION}" \
                                ${env.CUSTOMER != 'None' ? '--customer ' + env.CUSTOMER : ''}
                        """,
                        returnStdout: true
                    ).trim()
                    
                    env.LATEST_VERSION = versionOutput
                    echo "📦 版本号: ${env.LATEST_VERSION}"
                    
                    // 获取各项目的提交ID
                    dir("${WHALESCHEDULER_DIR}") {
                        env.WHALESCHEDULER_COMMIT_ID = sh(
                            script: "git rev-parse --short=7 HEAD",
                            returnStdout: true
                        ).trim()
                    }
                    
                    dir("${WHALESCHEDULER_UI_DIR}") {
                        env.WHALESCHEDULER_UI_COMMIT_ID = sh(
                            script: "git rev-parse --short=7 HEAD",
                            returnStdout: true
                        ).trim()
                    }
                    
                    dir("${WHALETUNNEL_DIR}") {
                        env.WHALETUNNEL_COMMIT_ID = sh(
                            script: "git rev-parse --short=7 HEAD",
                            returnStdout: true
                        ).trim()
                    }
                    
                    dir("${WHALETUNNEL_WEB_DIR}") {
                        env.WHALETUNNEL_WEB_COMMIT_ID = sh(
                            script: "git rev-parse --short=7 HEAD",
                            returnStdout: true
                        ).trim()
                    }
                    
                    env.COMMIT_IDS = "${env.WHALESCHEDULER_COMMIT_ID},${env.WHALESCHEDULER_UI_COMMIT_ID},${env.WHALETUNNEL_COMMIT_ID},${env.WHALETUNNEL_WEB_COMMIT_ID}"
                    
                    echo "📝 提交ID:"
                    echo "   WhaleScheduler: ${env.WHALESCHEDULER_COMMIT_ID}"
                    echo "   WhaleScheduler UI: ${env.WHALESCHEDULER_UI_COMMIT_ID}"
                    echo "   WhaleTunnel: ${env.WHALETUNNEL_COMMIT_ID}"
                    echo "   WhaleTunnel Web: ${env.WHALETUNNEL_WEB_COMMIT_ID}"
                    
                    // 更新提交ID到数据库
                    sh """
                        ${PYTHON_PATH} ${BUILD_SCRIPT} \
                            --operation update_commit_id \
                            --latest_version "${env.LATEST_VERSION}" \
                            --commit_id "${env.COMMIT_IDS}" \
                            ${env.CUSTOMER != 'None' ? '--customer ' + env.CUSTOMER : ''}
                    """
                }
            }
        }
        
        stage('Build Projects') {
            parallel {
                stage('Build WhaleScheduler') {
                    steps {
                        script {
                            retry(2) {
                                echo "🔨 构建 WhaleScheduler..."
                                dir("${WHALESCHEDULER_DIR}") {
                                    def buildCommand = "mvnd clean package -T2 -Dmaven.test.skip -Dcheckstyle.skip=true -Dspotless.skip=true -Prelease -Dseatunnel-framework.version=${env.LATEST_VERSION} -Dwhaletunnel-web.version=${env.LATEST_VERSION}"
                                    
                                    if (env.CUSTOMER != 'None') {
                                        buildCommand += " ${env.CUSTOMER}"
                                    }
                                    
                                    sh buildCommand
                                }
                                echo "✅ WhaleScheduler 构建完成"
                            }
                        }
                    }
                }
                
                stage('Build WhaleTunnel') {
                    steps {
                        script {
                            retry(2) {
                                echo "🔨 构建 WhaleTunnel..."
                                dir("${WHALETUNNEL_DIR}") {
                                    sh "mvn clean package -T2 -Dmaven.test.skip -Dcheckstyle.skip=true"
                                }
                                echo "✅ WhaleTunnel 构建完成"
                            }
                        }
                    }
                }
                
                stage('Build WhaleTunnel Web') {
                    steps {
                        script {
                            retry(2) {
                                echo "🔨 构建 WhaleTunnel Web..."
                                dir("${WHALETUNNEL_WEB_DIR}") {
                                    sh """
                                        if [ -f package.json ]; then
                                            npm install --registry=https://registry.npmmirror.com
                                            npm run build
                                        else
                                            mvn clean package -T2 -Dmaven.test.skip -Dcheckstyle.skip=true
                                        fi
                                    """
                                }
                                echo "✅ WhaleTunnel Web 构建完成"
                            }
                        }
                    }
                }
            }
        }
        
        stage('Package and Archive') {
            steps {
                script {
                    echo "📦 打包和归档..."
                    
                    // 创建三合一目录结构
                    sh "mkdir -p whalestudio_${env.LATEST_VERSION}/{whaletunnel,datasource,whalescheduler}"
                    
                    // 复制构建产物
                    sh """
                        # 复制 WhaleScheduler
                        find ${WHALESCHEDULER_DIR} -name "whalescheduler-dist-*.tar.gz" -exec cp {} whalestudio_${env.LATEST_VERSION}/whalescheduler/ \\;
                        
                        # 复制 WhaleTunnel
                        find ${WHALETUNNEL_DIR} -name "whaletunnel-dist-*.tar.gz" -exec cp {} whalestudio_${env.LATEST_VERSION}/whaletunnel/ \\;
                        
                        # 复制 WhaleTunnel Web (DataSource)
                        find ${WHALETUNNEL_WEB_DIR} -name "whaletunnel-web-dist-*.tar.gz" -exec cp {} whalestudio_${env.LATEST_VERSION}/datasource/ \\;
                    """
                    
                    // 创建最终安装包
                    def packageName = "whalestudio_${env.LATEST_VERSION}.tar.gz"
                    sh "tar -czf ${packageName} whalestudio_${env.LATEST_VERSION}/"
                    
                    env.PACKAGE_PATH = "${WORKSPACE}/${packageName}"
                    
                    echo "✅ 打包完成: ${packageName}"
                    
                    // 归档构建产物
                    archiveArtifacts artifacts: packageName, fingerprint: true
                }
            }
        }
        
        stage('Upload to OSS') {
            when {
                expression { env.UPDATE_TO_OSS == 'Yes' }
            }
            steps {
                script {
                    echo "☁️ 上传到OSS..."
                    
                    sh """
                        ${PYTHON_PATH} ${BUILD_SCRIPT} \
                            --operation upload_to_oss \
                            --file "${env.PACKAGE_PATH}" \
                            --latest_version "${env.LATEST_VERSION}" \
                            ${env.CUSTOMER != 'None' ? '--customer ' + env.CUSTOMER : ''}
                    """
                    
                    echo "✅ OSS上传完成"
                }
            }
        }
    }
    
    post {
        success {
            script {
                echo "🎉 构建成功完成!"
                
                sh """
                    ${PYTHON_PATH} ${BUILD_SCRIPT} \
                        --operation send_end_message \
                        --jenkins_url "${JENKINS_URL}" \
                        --branch "${env.BASELINE_VERSION}" \
                        --latest_version "${env.LATEST_VERSION}" \
                        --build_status success \
                        --build_path "${WORKSPACE}" \
                        --commit_id "${env.COMMIT_IDS}" \
                        ${env.CUSTOMER != 'None' ? '--customer ' + env.CUSTOMER : ''}
                """
            }
        }
        
        failure {
            script {
                echo "❌ 构建失败"
                
                sh """
                    ${PYTHON_PATH} ${BUILD_SCRIPT} \
                        --operation send_end_message \
                        --jenkins_url "${JENKINS_URL}" \
                        --branch "${env.BASELINE_VERSION ?: BRANCH}" \
                        --latest_version "${env.LATEST_VERSION ?: 'unknown'}" \
                        --build_status error \
                        --build_path "${WORKSPACE}" \
                        ${env.COMMIT_IDS ? '--commit_id ' + env.COMMIT_IDS : ''} \
                        ${env.CUSTOMER && env.CUSTOMER != 'None' ? '--customer ' + env.CUSTOMER : ''}
                """
                
                // 删除失败的版本记录
                if (env.LATEST_VERSION && env.LATEST_VERSION != 'unknown') {
                    sh """
                        ${PYTHON_PATH} ${BUILD_SCRIPT} \
                            --operation delete_version \
                            --latest_version "${env.LATEST_VERSION}" \
                            ${env.CUSTOMER != 'None' ? '--customer ' + env.CUSTOMER : ''}
                    """
                }
            }
        }
        
        aborted {
            script {
                echo "⚠️ 构建被中止"
                
                sh """
                    ${PYTHON_PATH} ${BUILD_SCRIPT} \
                        --operation send_end_message \
                        --jenkins_url "${JENKINS_URL}" \
                        --branch "${env.BASELINE_VERSION ?: BRANCH}" \
                        --latest_version "${env.LATEST_VERSION ?: 'unknown'}" \
                        --build_status cancel \
                        --build_path "${WORKSPACE}" \
                        ${env.COMMIT_IDS ? '--commit_id ' + env.COMMIT_IDS : ''} \
                        ${env.CUSTOMER && env.CUSTOMER != 'None' ? '--customer ' + env.CUSTOMER : ''}
                """
            }
        }
        
        always {
            script {
                echo "🧹 清理工作空间..."
                
                // 清理大文件，保留日志
                sh """
                    find . -name "*.tar.gz" -size +100M -delete 2>/dev/null || true
                    find . -name "target" -type d -exec rm -rf {} + 2>/dev/null || true
                    find . -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true
                """
            }
        }
    }
}
